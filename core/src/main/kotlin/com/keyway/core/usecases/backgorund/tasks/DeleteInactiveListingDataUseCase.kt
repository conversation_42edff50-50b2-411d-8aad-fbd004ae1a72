package com.keyway.core.usecases.backgorund.tasks

import com.keyway.core.dto.conciliation.DeleteInactiveListingDataInput
import com.keyway.core.ports.repositories.EffectiveRentRepository
import com.keyway.core.ports.repositories.ListingsRepository
import com.keyway.core.ports.repositories.TransactionRepository
import com.keyway.core.usecases.UseCase
import org.slf4j.LoggerFactory

class DeleteInactiveListingDataUseCase(
    private val listingsRepository: ListingsRepository,
    private val effectiveRepository: EffectiveRentRepository,
    private val transactionRepository: TransactionRepository,
) : UseCase<DeleteInactiveListingDataInput, Unit> {
    private val logger = LoggerFactory.getLogger(this.javaClass)

    override fun execute(input: DeleteInactiveListingDataInput) {
        val totalDeletedListings = deleteListingsAndEffective(input)

        // Delete inactive effective rent records in batches (handled internally by the repository)
        val totalDeletedEffective = effectiveRepository.deleteInactive(input.limit)

        logger.info("background_task:${input.requestType} - Deleted $totalDeletedListings listings || $totalDeletedEffective inactive effective")
    }

    private fun deleteListingsAndEffective(input: DeleteInactiveListingDataInput): Int {
        val batchSize = 1000
        var remainingLimit = input.limit
        var totalDeletedListings = 0

        // Process inactive listings in batches
        while (remainingLimit > 0) {
            val currentBatchSize = minOf(batchSize, remainingLimit)
            val listingsIds = listingsRepository.findInactiveListings(currentBatchSize).map { it.id }

            if (listingsIds.isEmpty()) {
                break // No more inactive listings to process
            }

            transactionRepository.executeTransaction {
                effectiveRepository.deleteByListingId(listingsIds)
                listingsRepository.delete(listingsIds)
            }

            totalDeletedListings += listingsIds.size
            remainingLimit -= listingsIds.size
        }
        return totalDeletedListings
    }
}
