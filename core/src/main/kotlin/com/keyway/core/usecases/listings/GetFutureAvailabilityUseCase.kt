package com.keyway.core.usecases.listings

import com.keyway.core.dto.query.metrics.IdType
import com.keyway.core.ports.repositories.ListingsRepository
import com.keyway.core.usecases.UseCase
import java.time.LocalDate

class GetFutureAvailabilityUseCase(
    private val listingsRepository: ListingsRepository,
) : UseCase<
        GetFutureAvailabilityUseCase.Input,
        Map<LocalDate, Int>,
    > {
    override fun execute(input: Input): Map<LocalDate, Int> = listingsRepository.futureAvailability(input.id, input.idType)

    data class Input(
        val id: String,
        val idType: IdType,
    )
}
