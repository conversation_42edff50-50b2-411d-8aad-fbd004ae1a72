package com.keyway.core.usecases.metrics

import com.keyway.core.dto.Metric
import com.keyway.core.dto.MetricDto
import com.keyway.core.dto.listings.input.AggregatedMetricType
import com.keyway.core.dto.listings.input.ComputeAggregatedGeoMetricInput
import com.keyway.core.dto.listings.output.BedroomGeoMetricsOutput
import com.keyway.core.dto.listings.output.BedroomRentGeoMetricOutput
import com.keyway.core.dto.listings.output.ByIdGeoMetricsOutput
import com.keyway.core.dto.listings.output.ByIdRentGeoMetricOutput
import com.keyway.core.dto.listings.output.GeoMetricDataOutput
import com.keyway.core.dto.listings.output.GeoMetricDetailOutput
import com.keyway.core.dto.listings.output.GeoRentMetricOutput
import com.keyway.core.dto.query.metrics.MetricsFiltersQuery
import com.keyway.core.entities.metric.MetricType
import com.keyway.core.exceptions.base.NotFoundException
import com.keyway.core.ports.repositories.MetricsRepository
import com.keyway.core.usecases.UseCaseAsync
import com.keyway.core.utils.DateUtils
import com.keyway.core.utils.distributeEvenly
import com.keyway.kommons.mapper.dataclass.mapTo
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.async
import kotlinx.coroutines.awaitAll
import kotlinx.coroutines.withContext
import java.math.BigDecimal
import java.math.RoundingMode
import java.time.LocalDate

class ComputeAggregatedGeoMetricUseCase(
    private val repository: MetricsRepository,
) : UseCaseAsync<ComputeAggregatedGeoMetricInput, List<GeoRentMetricOutput>> {
    override suspend fun execute(input: ComputeAggregatedGeoMetricInput): List<GeoRentMetricOutput> {
        val query = buildQuery(input)

        val partitionedIds = query.ids.distributeEvenly()

        val results =
            withContext(Dispatchers.IO) {
                val deferredResults =
                    partitionedIds.map { ids ->
                        async {
                            repository.aggregateMetrics(query.copy(ids = ids.toSet()))
                        }
                    }
                deferredResults.awaitAll().flatten()
            }

        return buildResult(
            query.dateFrom,
            query.dateTo,
            input.type,
            results,
        ).ifEmpty {
            throw NotFoundException("No metrics data for ids: ${input.ids}")
        }
    }

    private fun buildQuery(input: ComputeAggregatedGeoMetricInput): MetricsFiltersQuery =
        MetricsFiltersQuery(
            ids = input.ids,
            idType = input.idType,
            dateFrom = input.dateFrom ?: DateUtils.getDefaultDateFrom(),
            dateTo = input.dateTo ?: DateUtils.getDefaultDateTo(),
            type = MetricType.valueOf(input.type.toString()),
            rentListingType = null,
            unitCondition = null,
        )

    private fun buildResult(
        dateFrom: LocalDate,
        dateTo: LocalDate,
        type: AggregatedMetricType,
        results: List<MetricDto>,
    ): List<GeoRentMetricOutput> =
        when (type) {
            AggregatedMetricType.BEDROOMS ->
                mapMetrics(results, ::mapBedroomMetric) { propertyId, metrics ->
                    BedroomGeoMetricsOutput(propertyId, dateFrom, dateTo, metrics)
                }

            AggregatedMetricType.BY_ID ->
                mapMetrics(results, ::mapPropertyMetric) { propertyId, metrics ->
                    ByIdGeoMetricsOutput(propertyId, dateFrom, dateTo, metrics)
                }

            else -> emptyList()
        }

    private fun <T : GeoRentMetricOutput, U : GeoMetricDataOutput> mapMetrics(
        results: List<MetricDto>,
        metricsMapper: (MetricDto) -> U,
        outputConstructor: (String, List<U>) -> T,
    ): List<T> =
        results.groupBy { it.id }.map { (propertyId, metrics) ->
            val mappedMetrics = metrics.map(metricsMapper)
            outputConstructor(propertyId, mappedMetrics)
        }

    private fun mapBedroomMetric(metric: MetricDto): BedroomRentGeoMetricOutput =
        BedroomRentGeoMetricOutput(
            bedrooms = metric.bedrooms!!,
            askingRent = metric.askingRent.mapTo(),
            askingRentPSF = metric.askingRent.perSquareFootage(metric.squareFootage?.average),
            effectiveRent = metric.effectiveRent.mapTo(),
            effectiveRentPSF = metric.effectiveRent.perSquareFootage(metric.squareFootage?.average),
            recordsQuantity = metric.totalRecords,
            averageListingDays = metric.averageListingsDays.setScale(2, RoundingMode.HALF_UP),
            totalProperties = 0, // todo: change with new repo
            totalUnits = metric.totalUnits,
            squareFootage = metric.squareFootage?.mapTo(),
        )

    private fun mapPropertyMetric(metric: MetricDto): ByIdRentGeoMetricOutput =
        ByIdRentGeoMetricOutput(
            askingRent = metric.askingRent.mapTo(),
            askingRentPSF = metric.askingRent.perSquareFootage(metric.squareFootage?.average),
            effectiveRent = metric.effectiveRent.mapTo(),
            effectiveRentPSF = metric.effectiveRent.perSquareFootage(metric.squareFootage?.average),
            squareFootage = metric.squareFootage?.mapTo(),
            recordsQuantity = metric.totalRecords,
            averageListingDays = metric.averageListingsDays.setScale(2, RoundingMode.HALF_UP),
            totalProperties = 0, // todo: change with new repo
            totalUnits = metric.totalUnits,
        )

    private fun Metric.perSquareFootage(squareFootage: BigDecimal?): GeoMetricDetailOutput? =
        squareFootage.takeIf { it != null && it > BigDecimal.ZERO }?.let {
            GeoMetricDetailOutput(
                min = this.min.divide(squareFootage, RoundingMode.HALF_UP),
                max = this.max.divide(squareFootage, RoundingMode.HALF_UP),
                average = this.average.divide(squareFootage, RoundingMode.HALF_UP),
            )
        }
}
