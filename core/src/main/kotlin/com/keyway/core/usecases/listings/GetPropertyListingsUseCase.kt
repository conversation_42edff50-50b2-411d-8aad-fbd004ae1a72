package com.keyway.core.usecases.listings

import com.keyway.core.dto.listings.input.GetPropertyListingsInput
import com.keyway.core.dto.listings.output.RentListingWithEffectiveRentOutput
import com.keyway.core.dto.query.listings.PropertiesListingsQuery
import com.keyway.core.entities.RentListingType
import com.keyway.core.exceptions.base.NotFoundException
import com.keyway.core.ports.repositories.ListingsRepository
import com.keyway.core.usecases.UseCase
import com.keyway.core.utils.DateUtils
import kotlinx.coroutines.runBlocking

class GetPropertyListingsUseCase(
    private val listingsRepository: ListingsRepository,
    private val listingsWithEffectiveRentMapper: ListingsWithEffectiveRentMapper,
) : UseCase<GetPropertyListingsInput, List<RentListingWithEffectiveRentOutput>> {
    override fun execute(input: GetPropertyListingsInput): List<RentListingWithEffectiveRentOutput> =
        runBlocking {
            listingsRepository
                .findListingsForProperties(buildQuery(input))
                .ifEmpty {
                    throw NotFoundException("No listings data for propertyId: ${input.propertyId}")
                }.let(listingsWithEffectiveRentMapper::mapToOutput)
        }

    private fun getTypeId(input: GetPropertyListingsInput): String? =
        when (input.type) {
            RentListingType.FLOOR_PLAN -> input.floorPlan
            else -> input.unitId
        }

    private fun buildQuery(input: GetPropertyListingsInput): PropertiesListingsQuery =
        PropertiesListingsQuery(
            propertyIds = setOf(input.propertyId),
            type = input.type,
            typeId = getTypeId(input),
            floorPlan = input.floorPlan,
            dateFrom = input.dateFrom ?: DateUtils.getDefaultDateFrom(),
            dateTo = input.dateTo ?: DateUtils.getDefaultDateTo(),
            bedrooms = input.bedrooms,
            bathrooms = input.bathrooms,
        )
}
