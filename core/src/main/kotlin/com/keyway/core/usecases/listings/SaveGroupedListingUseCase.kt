package com.keyway.core.usecases.listings

import com.keyway.core.dto.listings.input.SaveRentGroupedListingInput
import com.keyway.core.dto.listings.input.UnitRecordsData
import com.keyway.core.dto.query.listings.PropertiesListingsQuery
import com.keyway.core.entities.EffectiveRent
import com.keyway.core.entities.RentListing
import com.keyway.core.ports.repositories.EffectiveRentRepository
import com.keyway.core.ports.repositories.ListingsRepository
import com.keyway.core.ports.repositories.TransactionRepository
import com.keyway.core.service.listing.GroupedListingService
import com.keyway.core.usecases.UseCase
import kotlinx.coroutines.runBlocking
import org.slf4j.LoggerFactory
import java.time.LocalDate

class SaveGroupedListingUseCase(
    private val listingsRepository: ListingsRepository,
    private val effectiveRentRepository: EffectiveRentRepository,
    private val groupedListingService: GroupedListingService,
    private val transactionRepository: TransactionRepository,
) : UseCase<SaveRentGroupedListingInput, Unit> {
    private val logger = LoggerFactory.getLogger(this::class.java)

    companion object {
        const val MAX_DAYS_PERIOD: Long = 7
        const val LISTING_PERSISTENCE_ERROR_PREFIX = "[LISTING_PERSISTENCE_ERROR]"
        const val IMPORT_PROCESS = "listing_data"
    }

    override fun execute(input: SaveRentGroupedListingInput) {
        if (input.records.isEmpty()) {
            logger.info("No records to process for property {} and {} {}", input.propertyId, input.type, input.typeId)
            return
        }

        runCatching {
            processListings(input)
        }.onFailure { error ->
            logger.warn(
                """$LISTING_PERSISTENCE_ERROR_PREFIX Failed to persist event for 
                    [${input.propertyId} || ${input.type.name} || ${input.typeId}]: ${error.message} - import_process:$IMPORT_PROCESS
                """.trimMargin(),
            )
        }
    }

    private fun processListings(input: SaveRentGroupedListingInput) {
        val incomingSortedRecords = input.records.sortedBy { it.recordDate }
        val (dateFrom, dateTo) = calculateDateRange(incomingSortedRecords)

        val existingListings =
            runBlocking {
                listingsRepository.findListingsForProperties(buildQuery(input, dateFrom, dateTo))
            }
        val existingEffectiveRent = fetchEffectiveRentListings(existingListings)

        val (rentListings, effectiveListings) =
            groupedListingService.processGroupedListing(
                input,
                existingListings,
                existingEffectiveRent,
                incomingSortedRecords,
            )

        persistListings(rentListings, effectiveListings, existingListings, existingEffectiveRent)
    }

    private fun persistListings(
        rentListings: List<RentListing>,
        effectiveListings: List<EffectiveRent>,
        periodExistingListings: List<RentListing>,
        periodExistingEffective: List<EffectiveRent>,
    ) {
        val (inactivesListing, inactivesEffective) =
            inactiveListingAndEffectiveData(rentListings, effectiveListings, periodExistingListings, periodExistingEffective)

        val filteredRentListings =
            rentListings.filter { newListing ->
                periodExistingListings.none { existingListing -> newListing == existingListing }
            }
        val filteredEffectiveListings =
            effectiveListings.filter { newEffective ->
                periodExistingEffective.none { existingEffective -> newEffective == existingEffective }
            }

        val (newListings, existingListings) = separateNewAndExistingListings(filteredRentListings, periodExistingListings)
        val (newEffective, existingEffective) = separateNewAndExistingEffective(filteredEffectiveListings, periodExistingEffective)

        val listingsToUpdate = existingListings.plus(inactivesListing)
        val effectiveToUpdate = existingEffective.plus(inactivesEffective)

        runCatching {
            transactionRepository.executeTransaction {
                if (listingsToUpdate.isNotEmpty()) {
                    listingsRepository.update(listingsToUpdate)
                }

                if (newListings.isNotEmpty()) {
                    listingsRepository.save(newListings)
                }

                if (effectiveToUpdate.isNotEmpty()) {
                    effectiveRentRepository.update(effectiveToUpdate)
                }

                if (newEffective.isNotEmpty()) {
                    effectiveRentRepository.save(newEffective)
                }
            }
        }.onFailure {
            val rentListing = rentListings.firstOrNull() ?: periodExistingListings.first()
            logger.warn(
                """ $LISTING_PERSISTENCE_ERROR_PREFIX - [${rentListing.propertyId} || ${rentListing.type.name} || ${rentListing.typeId}] 
                Error during persistence: ${it.message} - import_process:$IMPORT_PROCESS """,
            )
        }
    }

    private fun separateNewAndExistingListings(
        rentListings: List<RentListing>,
        periodExistingListings: List<RentListing>,
    ): Pair<List<RentListing>, List<RentListing>> = rentListings.partition { listing -> periodExistingListings.none { it.id == listing.id } }

    private fun separateNewAndExistingEffective(
        effectiveListings: List<EffectiveRent>,
        periodExistingEffective: List<EffectiveRent>,
    ): Pair<List<EffectiveRent>, List<EffectiveRent>> = effectiveListings.partition { effective -> periodExistingEffective.none { it.id == effective.id } }

    private fun fetchEffectiveRentListings(periodExistingListings: List<RentListing>) =
        periodExistingListings
            .takeIf { it.isNotEmpty() }
            ?.map { it.id }
            ?.let { listingIds -> runBlocking { effectiveRentRepository.getByListingIds(listingIds, null, null, isActiveOnly = false) } }
            ?.sortedBy { it.dateFrom }
            ?: emptyList()

    private fun inactiveListingAndEffectiveData(
        listings: List<RentListing>,
        effectiveListings: List<EffectiveRent>,
        periodExistingListings: List<RentListing>,
        periodExistingEffective: List<EffectiveRent>,
    ): Pair<List<RentListing>, List<EffectiveRent>> {
        val conciliatedIds = listings.mapTo(HashSet()) { it.id }
        val conciliatedEffectiveIds = effectiveListings.mapTo(HashSet()) { it.id }
        val listingDateFrom = periodExistingListings.map { listing -> listing.dateFrom }.toMutableSet()
        val effectiveDateFrom = periodExistingEffective.map { listing -> listing.dateFrom }.toMutableSet()

        return periodExistingListings
            .asSequence()
            .filterNot { it.id in conciliatedIds && it.isActive }
            .map {
                it.copy(
                    dateTo = it.dateFrom.minusYears(50),
                    dateFrom =
                        getPastDateFrom(it.dateFrom, listingDateFrom)
                            .also(listingDateFrom::add),
                    isActive = false,
                )
            }.toList() to
            periodExistingEffective
                .asSequence()
                .filter {
                    (
                        !conciliatedEffectiveIds.contains(it.id) ||
                            !conciliatedIds.contains(it.rentListingId)
                    ) &&
                        it.isActive
                }.map {
                    it.copy(
                        dateTo = it.dateFrom.minusYears(50),
                        dateFrom =
                            getPastDateFrom(it.dateFrom, effectiveDateFrom)
                                .also(effectiveDateFrom::add),
                        isActive = false,
                    )
                }.toList()
    }

    private fun getPastDateFrom(
        date: LocalDate,
        usedDateFrom: Set<LocalDate>,
    ): LocalDate =
        date.minusYears(50).takeUnless { usedDateFrom.contains(it) }
            ?: getPastDateFrom(date.minusDays(1), usedDateFrom)

    private fun calculateDateRange(sortedRecords: List<UnitRecordsData>): Pair<LocalDate, LocalDate> =
        sortedRecords.first().recordDate.minusDays(MAX_DAYS_PERIOD) to sortedRecords.last().recordDate.plusDays(MAX_DAYS_PERIOD)

    private fun buildQuery(
        input: SaveRentGroupedListingInput,
        minDate: LocalDate,
        maxDate: LocalDate,
    ) = PropertiesListingsQuery(
        propertyIds = setOf(input.propertyId),
        type = input.type,
        typeId = input.typeId,
        dateFrom = minDate,
        dateTo = maxDate,
        bathrooms = null,
        bedrooms = null,
        floorPlan = null,
    )
}
