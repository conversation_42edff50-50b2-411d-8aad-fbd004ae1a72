package com.keyway.core.usecases.backgorund.tasks

import com.keyway.core.entities.property.PropertyLastSeenData
import com.keyway.core.ports.repositories.PropertyMetricsRepository
import com.keyway.core.service.MessageBatchPublisher
import com.keyway.core.usecases.UseCase
import com.keyway.kommons.mapper.JsonMapper
import kotlinx.coroutines.channels.Channel
import kotlinx.coroutines.channels.ReceiveChannel
import kotlinx.coroutines.channels.SendChannel
import kotlinx.coroutines.launch
import kotlinx.coroutines.runBlocking
import org.slf4j.LoggerFactory

class NotifyLastRentByPropertyUseCase(
    private val messageBatchPublisher: MessageBatchPublisher,
    private val propertyMetricsRepository: PropertyMetricsRepository,
) : UseCase<NotifyLastRentByPropertyUseCase.Input, Unit> {
    data class Input(
        val propertyIds: Set<String> = emptySet(),
        val zipCodes: Set<String> = emptySet(),
    )

    private val logger = LoggerFactory.getLogger(this::class.java)

    companion object {
        private const val LIMIT = 1000
    }

    override fun execute(input: Input) =
        runBlocking {
            val channel = Channel<List<PropertyLastSeenData>>(capacity = Channel.UNLIMITED)

            val producerJob = launch { produceMetrics(channel) }
            val consumerJob = launch { consumeMetrics(channel) }

            producerJob.join()
            consumerJob.join()
        }

    private suspend fun produceMetrics(channel: SendChannel<List<PropertyLastSeenData>>) {
        var offset = 0
        var sended = 0
        while (true) {
            val data =
                propertyMetricsRepository.getLastSeenByProperty(
                    offset = offset,
                    limit = LIMIT,
                )

            if (data.isEmpty()) break

            channel.send(data)
            offset += LIMIT
            sended += data.size
        }
        logger.info("BACKGROUND TASK NOTIFY LAST RENT, HAD SEND $sended PROPERTIES")
        channel.close()
    }

    private suspend fun consumeMetrics(channel: ReceiveChannel<List<PropertyLastSeenData>>) {
        for (data in channel) {
            messageBatchPublisher.publish(data.map { JsonMapper.encode(it) })
        }
    }
}
