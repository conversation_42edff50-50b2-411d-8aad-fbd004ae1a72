package com.keyway.core.usecases.listings

import com.keyway.core.dto.listings.input.CalculateRentSuggestionInput
import com.keyway.core.dto.listings.input.GetPropertiesListingsInput
import com.keyway.core.dto.listings.output.CalculateRentSuggestionOutput
import com.keyway.core.dto.listings.output.UnitComparison
import com.keyway.core.dto.listings.output.UnitRentSuggestionOutput
import com.keyway.core.entities.EffectiveRent
import com.keyway.core.entities.RentListing
import com.keyway.core.entities.UnitListingData
import com.keyway.core.entities.property.PropertyUnit
import com.keyway.core.ports.repositories.EffectiveRentRepository
import com.keyway.core.ports.repositories.PropertyUnitRepository
import com.keyway.core.ports.repositories.UnitSimilarityCalculatorConfigRepository
import com.keyway.core.service.listing.GetPropertiesListingService
import com.keyway.core.service.similarity.GaussianUnitSimilarityCalculator
import com.keyway.core.service.similarity.UnitSimilarity
import com.keyway.core.usecases.UseCase
import com.keyway.core.utils.average
import kotlinx.coroutines.runBlocking
import org.slf4j.LoggerFactory
import java.math.BigDecimal
import java.math.RoundingMode

class CalculateRentSuggestionUseCase(
    private val propertyUnitRepository: PropertyUnitRepository,
    private val getPropertiesListingService: GetPropertiesListingService,
    private val effectiveRentRepository: EffectiveRentRepository,
    private val unitSimilarityCalculatorConfigRepository: UnitSimilarityCalculatorConfigRepository,
) : UseCase<CalculateRentSuggestionInput, CalculateRentSuggestionOutput> {
    private val logger = LoggerFactory.getLogger(this::class.java)

    override fun execute(input: CalculateRentSuggestionInput): CalculateRentSuggestionOutput =
        runCatching {
            val propertiesData =
                propertyUnitRepository
                    .findByProperties(input.getAllPropertyIds())
                    .takeIf {
                        it.isNotEmpty()
                    } ?: return emptyRentSuggestion(input.basePropertyId)

            val listings =
                getPropertiesListingService.all(
                    GetPropertiesListingsInput(
                        propertyIds = input.getAllPropertyIds(),
                        dateFrom = input.dateFrom,
                        dateTo = input.dateTo,
                    ),
                )

            val (baseUnits, compsUnits) = buildBaseAndCompsUnitsListingData(propertiesData, listings, input)

            val similarUnits = getSimilarityCalculator(input).calculateUnitSimilarity(baseUnits, compsUnits)
            val effectiveRents =
                runBlocking {
                    effectiveRentRepository
                        .getByListingIds(listings.map { it.id }, dateFrom = input.dateFrom, dateTo = input.dateTo)
                        .groupBy { it.rentListingId }
                }

            return CalculateRentSuggestionOutput(
                propertyId = input.basePropertyId,
                units =
                    calculateUnitRentSuggestions(
                        similarUnits,
                        effectiveRents,
                    ),
            )
        }.onFailure { logger.error("Error calculating rent suggestion", it) }
            .getOrDefault(emptyRentSuggestion(input.basePropertyId))

    private fun buildBaseAndCompsUnitsListingData(
        propertiesData: List<PropertyUnit>,
        listings: List<RentListing>,
        input: CalculateRentSuggestionInput,
    ): Pair<List<UnitListingData>, List<UnitListingData>> {
        val baseUnits = mutableListOf<UnitListingData>()
        val compsUnits = mutableListOf<UnitListingData>()
        propertiesData.forEach {
            val unitListingData =
                UnitListingData(
                    unit = it,
                    listings =
                        listings.filter { f ->
                            f.propertyId == it.propertyId &&
                                f.typeId == it.unitId
                        },
                )
            when {
                it.propertyId == input.basePropertyId -> baseUnits.add(unitListingData)
                unitListingData.listings.isNotEmpty() -> compsUnits.add(unitListingData)
                else -> Unit
            }
        }

        logger.info(
            "Found ${propertiesData.size} units // base units -> ${baseUnits.size} // comps units with rent -> ${compsUnits.size}",
        )

        return Pair(baseUnits, compsUnits)
    }

    private fun getSimilarityCalculator(input: CalculateRentSuggestionInput): GaussianUnitSimilarityCalculator {
        val calculatorConfig = input.organizationId?.let(unitSimilarityCalculatorConfigRepository::get) ?: unitSimilarityCalculatorConfigRepository.getDefault()
        logger.info("Using config $calculatorConfig")
        return GaussianUnitSimilarityCalculator(calculatorConfig)
    }

    private fun emptyRentSuggestion(basePropertyId: String) = CalculateRentSuggestionOutput(propertyId = basePropertyId, units = emptyList())

    private fun calculateUnitRentSuggestions(
        filteredScores: List<UnitSimilarity>,
        groupedEffectiveRent: Map<String, List<EffectiveRent>>,
    ): List<UnitRentSuggestionOutput> =
        filteredScores.map { unitSimilarity ->

            val averageUnitsSqft = unitSimilarity.similarUnits.mapNotNull { unit -> unit.unit.squareFootage }.average()
            val effectiveRents =
                unitSimilarity.similarUnits
                    .flatMap {
                        it.listings.map { listing -> listing.id }
                    }.flatMap {
                        groupedEffectiveRent[it] ?: emptyList()
                    }

            UnitRentSuggestionOutput(
                unitId = unitSimilarity.baseUnit.unitId,
                askingRent = calculateAskingRent(unitSimilarity, averageUnitsSqft),
                effectiveRent = calculateEffectiveRent(unitSimilarity, effectiveRents, averageUnitsSqft),
                similarUnitsComparisons = mapToUnitComparison(unitSimilarity),
            )
        }

    private fun mapToUnitComparison(unitSimilarity: UnitSimilarity): List<UnitComparison> =
        unitSimilarity.similarUnits.map { similarity ->
            UnitComparison(
                propertyId = similarity.unit.propertyId,
                unitId = similarity.unit.unitId,
                similarityScore = similarity.score,
            )
        }

    private fun calculateAskingRent(
        unitSimilarity: UnitSimilarity,
        averageUnitsSqft: Double,
    ): BigDecimal {
        val similarListings =
            unitSimilarity.similarUnits.flatMap { similar -> similar.listings }

        if (similarListings.isEmpty()) {
            return BigDecimal.ZERO
        }

        val averageRents = similarListings.map { it.rent.value }.average()
        return getPriceFromAverage(averageRents, averageUnitsSqft, unitSimilarity.baseUnit.squareFootage)
    }

    private fun calculateEffectiveRent(
        unitSimilarity: UnitSimilarity,
        effectiveRents: List<EffectiveRent>,
        averageUnitsSqft: Double,
    ): BigDecimal {
        if (effectiveRents.isEmpty()) {
            return BigDecimal.ZERO
        }

        val avgEffectiveRents = effectiveRents.map { it.rent.value }.average()

        return getPriceFromAverage(avgEffectiveRents, averageUnitsSqft, unitSimilarity.baseUnit.squareFootage)
    }

    private fun getPriceFromAverage(
        averageUnitsRent: Double,
        averageUnitsSqft: Double,
        sourceUnitSqft: BigDecimal?,
    ): BigDecimal {
        val rentPrice =
            sourceUnitSqft?.times(averageUnitsRent.div(averageUnitsSqft).toBigDecimal())
                ?: averageUnitsRent.toBigDecimal()

        return rentPrice.setScale(2, RoundingMode.HALF_UP)
    }
}
