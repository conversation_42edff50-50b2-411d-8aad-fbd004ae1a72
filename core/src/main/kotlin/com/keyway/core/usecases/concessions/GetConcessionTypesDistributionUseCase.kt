package com.keyway.core.usecases.concessions

import com.keyway.core.dto.query.metrics.IdType
import com.keyway.core.entities.concessions.ConcessionTypesDistribution
import com.keyway.core.ports.repositories.PropertyConcessionV2Repository
import com.keyway.core.usecases.UseCase
import java.time.LocalDate

class GetConcessionTypesDistributionUseCase(
    private val repository: PropertyConcessionV2Repository,
) : UseCase<GetConcessionTypesDistributionUseCase.Input, ConcessionTypesDistribution> {
    override fun execute(input: Input): ConcessionTypesDistribution =
        repository.getConcessionTypes(
            idType = input.idType,
            id = input.id,
            dateFrom = input.dateFrom,
            dateTo = input.dateTo,
        )

    data class Input(
        val idType: IdType,
        val id: String,
        val dateFrom: LocalDate,
        val dateTo: LocalDate,
    )
}
