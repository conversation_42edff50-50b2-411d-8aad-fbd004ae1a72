package com.keyway.core.usecases.concessions

import com.keyway.core.dto.concessions.input.GetPropertiesConcessionsV2Input
import com.keyway.core.dto.query.concessions.ConcessionsQuery
import com.keyway.core.entities.concessions.PropertyConcessionV2
import com.keyway.core.ports.repositories.PropertyConcessionV2Repository
import com.keyway.core.usecases.UseCase
import com.keyway.core.utils.DateUtils
import java.time.LocalDate

class GetPropertiesConcessionsV2UseCase(
    private val repository: PropertyConcessionV2Repository,
) : UseCase<GetPropertiesConcessionsV2Input, List<PropertyConcessionV2>> {
    override fun execute(input: GetPropertiesConcessionsV2Input): List<PropertyConcessionV2> =
        repository.findByPropertiesIds(buildQuery(input)).let { concessions ->

            val maxActiveByPropertyId = concessions.maxDateToByActiveAndPropertyId()

            concessions.forEach {
                it.active = it.shouldBeActive() && it.dateTo == maxActiveByPropertyId[it.propertyId]
            }

            when (input.isActive) {
                true -> concessions.filter { it.active }
                false -> concessions.filter { !it.active }
                else -> concessions
            }
        }

    private fun buildQuery(input: GetPropertiesConcessionsV2Input): ConcessionsQuery {
        val (dateFrom, dateTo) = extractProperDates(input)
        return ConcessionsQuery(
            propertyIds = input.propertyIds,
            dateFrom = dateFrom,
            dateTo = dateTo,
            isActive = input.isActive,
            benefit = null,
            type = null,
        )
    }

    private fun List<PropertyConcessionV2>.maxDateToByActiveAndPropertyId(): Map<String, LocalDate> = this.groupBy { it.propertyId }.mapValues { it.value.maxBy { conn -> conn.dateTo }.dateTo }

    private fun extractProperDates(input: GetPropertiesConcessionsV2Input): Pair<LocalDate, LocalDate> =
        Pair(input.dateFrom ?: DateUtils.getDefaultDateFrom(), input.dateTo ?: DateUtils.getDefaultDateTo())
}
