package com.keyway.core.usecases.multifamily.units

import com.keyway.core.entities.property.PropertyUnit
import com.keyway.core.ports.repositories.PropertyUnitRepository
import com.keyway.core.usecases.UseCase

class SaveOrUpdatePropertyUnits(
    private val propertyUnitRepository: PropertyUnitRepository,
) : UseCase<List<PropertyUnit>, Unit> {
    override fun execute(input: List<PropertyUnit>) {
        input.forEach(propertyUnitRepository::saveOrUpdate)
    }
}
