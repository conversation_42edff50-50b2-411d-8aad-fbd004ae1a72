package com.keyway.core.usecases.listings

import com.keyway.core.dto.listings.input.GetPropertyRentDataInput
import com.keyway.core.dto.listings.output.RentListingWithEffectiveRentOutput
import com.keyway.core.dto.query.listings.PropertiesListingsQuery
import com.keyway.core.exceptions.base.NotFoundException
import com.keyway.core.ports.repositories.ListingsRepository
import com.keyway.core.usecases.UseCase
import kotlinx.coroutines.runBlocking

class GetPropertyLastListingUseCase(
    private val listingsRepository: ListingsRepository,
    private val listingsWithEffectiveRentMapper: ListingsWithEffectiveRentMapper,
) : UseCase<GetPropertyRentDataInput, List<RentListingWithEffectiveRentOutput>> {
    override fun execute(input: GetPropertyRentDataInput): List<RentListingWithEffectiveRentOutput> =
        runBlocking {
            listingsRepository
                .findLastListingByProperties(
                    PropertiesListingsQuery(
                        propertyIds = setOf(input.propertyId),
                        typeId = null,
                        type = null,
                        floorPlan = null,
                        dateFrom = input.dateFrom,
                        dateTo = input.dateTo,
                        bedrooms = null,
                        bathrooms = null,
                    ),
                ).ifEmpty {
                    throw NotFoundException("No unit rent data for propertyId: ${input.propertyId}")
                }.let(listingsWithEffectiveRentMapper::mapToOutput)
        }
}
