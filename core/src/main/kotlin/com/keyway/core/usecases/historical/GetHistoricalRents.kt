package com.keyway.core.usecases.historical

import com.keyway.core.dto.historical.HistoricalRentDto
import com.keyway.core.dto.historical.HistoricalRentOutput
import com.keyway.core.dto.historical.HistoricalRentSummaryDto
import com.keyway.core.dto.query.metrics.IdType
import com.keyway.core.entities.HistoricalPeriodicity
import com.keyway.core.entities.RentType
import com.keyway.core.entities.UnitCondition
import com.keyway.core.ports.repositories.HistoricalRentRepository
import com.keyway.core.usecases.UseCaseAsync
import com.keyway.core.utils.distributeEvenly
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.async
import kotlinx.coroutines.awaitAll
import kotlinx.coroutines.withContext
import java.math.BigDecimal
import java.math.RoundingMode
import java.time.LocalDate

class GetHistoricalRents(
    private val historicalRentRepository: HistoricalRentRepository,
) : UseCaseAsync<GetHistoricalRents.Input, List<HistoricalRentSummaryDto>> {
    data class Input(
        val ids: Set<String>,
        val idType: IdType,
        val dateFrom: LocalDate,
        val dateTo: LocalDate,
        val periodicity: HistoricalPeriodicity,
        val rentType: RentType,
        val bedrooms: Int?,
        val bathrooms: BigDecimal?,
        val unitCondition: UnitCondition?,
    )

    override suspend fun execute(input: Input): List<HistoricalRentSummaryDto> =
        input.ids
            .distributeEvenly()
            .let { partitionedIds ->
                withContext(Dispatchers.IO) {
                    val deferredResults =
                        partitionedIds.map { ids ->
                            async {
                                historicalRentRepository
                                    .getHistoricalRents(
                                        ids = ids.toSet(),
                                        idType = input.idType,
                                        dateFrom = input.dateFrom,
                                        dateTo = input.dateTo,
                                        rentType = input.rentType,
                                        periodicity = input.periodicity,
                                        bedrooms = input.bedrooms,
                                        bathrooms = input.bathrooms,
                                        unitCondition = input.unitCondition,
                                    )
                            }
                        }
                    deferredResults.awaitAll().flatten()
                }
            }.groupBy {
                it.id
            }.map { (id, values) ->
                buildSummary(values, input.rentType, id)
            }

    private fun buildSummary(
        rents: List<HistoricalRentOutput>,
        rentType: RentType,
        id: String,
    ): HistoricalRentSummaryDto {
        val rentChange = rents.getRentChange()
        val historicalRents = rents.toDto()

        return HistoricalRentSummaryDto(
            id = id,
            rentType = rentType,
            rentChange = rentChange,
            historicalRents = historicalRents,
        )
    }

    private fun List<HistoricalRentOutput>.toDto(): List<HistoricalRentDto> =
        this
            .groupBy { it.dateFrom to it.dateTo }
            .mapValues { (key, value) ->
                HistoricalRentDto(
                    dateFrom = key.first,
                    dateTo = key.second,
                    averageRent = value.map { it.rentAverage }.avg(),
                    medianRent = value.mapNotNull { it.rentMedian }.takeUnless { it.isEmpty() }?.avg(),
                    averageRentPSF =
                        value
                            .mapNotNull { safeDiv(it.rentAverage, it.squareFootageAverage) }
                            .takeUnless { it.isEmpty() }
                            ?.avg(),
                    medianRentPSF =
                        value
                            .mapNotNull { safeDiv(it.rentMedian, it.squareFootageAverage) }
                            .takeUnless { it.isEmpty() }
                            ?.avg(),
                )
            }.values
            .toList()

    private fun safeDiv(
        value: BigDecimal?,
        divisor: BigDecimal?,
    ): BigDecimal? =
        divisor
            ?.takeIf { it > BigDecimal.ZERO }
            ?.let { value?.divide(divisor, 2, RoundingMode.HALF_UP) }

    private fun List<HistoricalRentOutput>.getRentChange(): BigDecimal =
        this
            .groupBy { it.bedrooms to it.bathrooms }
            .mapValues { (_, value) ->
                val min = value.minBy { it.dateTo }
                val max = value.maxBy { it.dateTo }
                (max.rentAverage - min.rentAverage) / min.rentAverage
            }.values
            .avg()

    private fun Collection<BigDecimal>.avg() =
        this
            .reduce(BigDecimal::add)
            .divide(BigDecimal(this.size), 2, RoundingMode.HALF_UP)
}
