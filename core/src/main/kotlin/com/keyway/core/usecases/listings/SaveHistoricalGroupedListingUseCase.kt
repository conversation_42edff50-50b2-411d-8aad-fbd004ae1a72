package com.keyway.core.usecases.listings

import com.keyway.core.dto.listings.input.SaveRentGroupedListingInput
import com.keyway.core.dto.listings.input.UnitRecordsData
import com.keyway.core.dto.query.listings.PropertiesListingsQuery
import com.keyway.core.entities.Money
import com.keyway.core.entities.RentListing
import com.keyway.core.entities.RentListingType
import com.keyway.core.ports.repositories.HistoricDataRepository
import com.keyway.core.ports.repositories.ListingsRepository
import com.keyway.core.usecases.UseCase
import com.keyway.core.utils.DateUtils
import com.keyway.core.utils.DateUtils.equalMonthYear
import com.keyway.core.utils.DateUtils.isBetween
import com.keyway.core.utils.DateUtils.toEndOfMonth
import com.keyway.core.utils.IdGenerator
import kotlinx.coroutines.runBlocking

class SaveHistoricalGroupedListingUseCase(
    private val listingsRepository: ListingsRepository,
    private val historicDataRepository: HistoricDataRepository,
    private val idGenerator: IdGenerator,
) : UseCase<SaveRentGroupedListingInput, Unit> {
    override fun execute(input: SaveRentGroupedListingInput) {
        val existingListings = getListings(input)
        val recordsToSave =
            input.records.filter { record ->
                existingListings.none {
                    record.recordDate.isBetween(it.dateFrom, it.dateTo) ||
                        record.recordDate.equalMonthYear(it.dateFrom) ||
                        record.recordDate.equalMonthYear(it.dateTo)
                }
            }
        if (recordsToSave.isEmpty()) {
            return
        }
        val listings = recordsToSave.map { mapInputToListing(input, it) }
        historicDataRepository.saveListing(listings)
    }

    private fun mapInputToListing(
        input: SaveRentGroupedListingInput,
        record: UnitRecordsData,
    ): RentListing =
        RentListing(
            id = idGenerator.invoke(),
            propertyId = input.propertyId,
            type = input.type,
            typeId = input.typeId,
            dateFrom = record.recordDate,
            dateTo = record.recordDate.toEndOfMonth(),
            recordSource = record.recordSource,
            zipCode = input.zipCode,
            msaCode = input.msaCode,
            unitSquareFootage = input.unitSquareFootage,
            bedroomsQuantity = input.bedroomsQuantity,
            bathroomsQuantity = input.bathroomsQuantity,
            floorPlan = input.floorPlan,
            availableIn = record.availableIn?.takeIf { it.isAfter(DateUtils.now()) },
            rent = Money.of(record.rent),
            rentDeposit = record.rentDeposit?.let { Money.of(it) },
            createdAt = DateUtils.now(),
            updateAt = DateUtils.now(),
        )

    private fun getListings(input: SaveRentGroupedListingInput) =
        runBlocking {
            listingsRepository.findListingsForProperties(
                propertiesListingsQuery =
                    PropertiesListingsQuery(
                        propertyIds = setOf(input.propertyId),
                        type = RentListingType.UNIT,
                        typeId = null,
                        floorPlan = null,
                        dateFrom = input.records.minBy { it.recordDate }.recordDate,
                        dateTo =
                            input.records
                                .maxBy { it.recordDate }
                                .recordDate
                                .toEndOfMonth(),
                        bedrooms = null,
                        bathrooms = null,
                    ),
            )
        }
}
