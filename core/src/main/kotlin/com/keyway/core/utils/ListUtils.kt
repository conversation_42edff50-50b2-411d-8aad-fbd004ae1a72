package com.keyway.core.utils

import java.math.BigDecimal

fun <T> Collection<T>.distributeEvenly(maxItemsPerPartition: Int = 5): List<List<T>> {
    require(maxItemsPerPartition > 0) { "Max items per partition must be positive" }
    val values = this.toList()
    return when (val partitions = (size + maxItemsPerPartition - 1) / maxItemsPerPartition) {
        1 -> listOf(values)
        else -> {
            val itemsPerPartition = size / partitions
            val remainder = size % partitions

            chunked(itemsPerPartition + if (remainder > 0) 1 else 0)
                .take(partitions)
        }
    }
}

fun List<BigDecimal>.average(): Double =
    if (isEmpty()) {
        0.0
    } else {
        map { it.toDouble() }.average()
    }
