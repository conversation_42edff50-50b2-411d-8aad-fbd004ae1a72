package com.keyway.core.utils

import java.time.Clock
import java.time.Instant
import java.time.LocalDate
import java.time.LocalDateTime
import java.time.OffsetDateTime
import java.time.chrono.ChronoLocalDate
import java.time.chrono.ChronoLocalDateTime

object DateUtils {
    private const val DEFAULT_DAYS: Long = 30

    private lateinit var clock: Clock

    fun initialize(clock: Clock) {
        this.clock = clock
    }

    fun setClock(newClock: Clock) {
        clock = newClock
    }

    fun getClock(): Clock {
        if (::clock.isInitialized) {
            return clock
        }
        return Clock.systemDefaultZone()
    }

    inline fun <reified T> now(): T =
        when (T::class) {
            LocalDate::class, ChronoLocalDate::class -> LocalDate.now(getClock()) as T
            LocalDateTime::class, ChronoLocalDateTime::class -> LocalDateTime.now(getClock()) as T
            OffsetDateTime::class -> OffsetDateTime.now(getClock()) as T
            Instant::class -> Instant.now(getClock()) as T
            else -> throw UnsupportedOperationException("Unsupported Temporal type: ${T::class.simpleName}")
        }

    fun getDefaultDateFrom(): LocalDate = now<LocalDate>().minusDays(DEFAULT_DAYS)

    fun getDefaultDateTo(): LocalDate = now<LocalDate>()

    fun generateDateRange(
        start: LocalDate,
        end: LocalDate,
        step: Long = 1L,
    ): Sequence<LocalDate> = generateSequence(start) { it.plusDays(step) }.takeWhile { it <= end }

    fun LocalDate.isBetween(
        startDate: LocalDate,
        endDate: LocalDate,
    ): Boolean =
        (this.isEqual(startDate) || this.isAfter(startDate)) &&
            (this.isEqual(endDate) || this.isBefore(endDate))

    fun LocalDate.equalMonthYear(date: LocalDate): Boolean =
        this.month == date.month &&
            this.year == date.year

    fun LocalDate.toEndOfMonth(): LocalDate =
        LocalDate
            .of(this.year, this.month, 1)
            .plusMonths(1)
            .minusDays(1)
}
