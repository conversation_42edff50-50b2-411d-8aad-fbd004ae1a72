package com.keyway.core.ports.repositories

import com.keyway.core.entities.property.PropertyUnit

interface PropertyUnitRepository {
    fun saveOrUpdate(propertyUnit: PropertyUnit)

    fun findByPropertyId(propertyId: String): List<PropertyUnit>

    fun findByPropertyIdAndUnitId(
        propertyId: String,
        unitId: String,
    ): PropertyUnit?

    fun findByProperties(propertiesIds: Set<String>): List<PropertyUnit>
}
