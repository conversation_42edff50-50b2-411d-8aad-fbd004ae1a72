package com.keyway.core.ports.repositories

import com.keyway.core.entities.EffectiveRent
import java.time.LocalDate

interface EffectiveRentRepository {
    fun save(effectiveRent: EffectiveRent)

    fun save(effectiveRents: List<EffectiveRent>)

    fun update(effectiveRents: List<EffectiveRent>)

    fun getById(effectiveRentId: String): EffectiveRent

    fun getByListingId(listingId: String): List<EffectiveRent>

    suspend fun getByListingIds(
        listingIds: List<String>,
        dateFrom: LocalDate?,
        dateTo: LocalDate?,
        isActiveOnly: Boolean = true,
    ): List<EffectiveRent>

    fun deleteById(ids: List<String>)

    fun deleteByListingId(ids: List<String>)

    fun deleteInactive(limit: Int): Int
}
