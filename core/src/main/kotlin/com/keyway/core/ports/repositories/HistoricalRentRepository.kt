package com.keyway.core.ports.repositories

import com.keyway.core.dto.historical.HistoricalRentOutput
import com.keyway.core.dto.query.metrics.IdType
import com.keyway.core.entities.HistoricalRentPeriodicity
import com.keyway.core.entities.RentType
import com.keyway.core.entities.UnitCondition
import java.math.BigDecimal
import java.time.LocalDate

interface HistoricalRentRepository {
    suspend fun getHistoricalRents(
        ids: Set<String>,
        idType: IdType,
        dateFrom: LocalDate,
        dateTo: LocalDate,
        periodicity: HistoricalRentPeriodicity,
        rentType: RentType,
        bedrooms: Int?,
        bathrooms: BigDecimal?,
        unitCondition: UnitCondition?,
    ): List<HistoricalRentOutput>
}
