package com.keyway.core.ports.repositories

import com.keyway.core.dto.query.concessions.ConcessionsQuery
import com.keyway.core.entities.concessions.PropertyConcessionV2
import java.time.LocalDate

interface PropertyConcessionV2Repository {
    fun saveOrUpdate(propertyConcession: PropertyConcessionV2)

    fun findByPropertyId(
        propertyId: String,
        dateFrom: LocalDate,
        dateTo: LocalDate,
    ): List<PropertyConcessionV2>

    fun findMostRecentConcessionByPropertyId(
        propertyId: String,
        dateFrom: LocalDate,
    ): PropertyConcessionV2?

    fun findByPropertiesIds(query: ConcessionsQuery): List<PropertyConcessionV2>

    fun findActiveConcessionsByPropertiesIds(query: ConcessionsQuery): List<PropertyConcessionV2>

    fun delete(ids: List<String>)
}
