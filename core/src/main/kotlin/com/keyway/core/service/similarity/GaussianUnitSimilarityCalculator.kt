package com.keyway.core.service.similarity

import com.keyway.core.entities.UnitListingData
import com.keyway.core.entities.config.UnitSimilarityCalculatorConfig
import com.keyway.core.entities.property.PropertyUnit
import kotlin.math.abs
import kotlin.math.exp
import kotlin.math.pow

class GaussianUnitSimilarityCalculator(
    private val unitSimilarityCalculatorConfig: UnitSimilarityCalculatorConfig,
) {
    fun calculateUnitSimilarity(
        baseUnits: List<UnitListingData>,
        compsUnits: List<UnitListingData>,
    ): List<UnitSimilarity> =
        baseUnits.map { baseUnit ->
            UnitSimilarity(
                baseUnit = baseUnit.unit,
                similarUnits = generateSimilarityScore(baseUnit.unit, compsUnits),
            )
        }

    private fun generateSimilarityScore(
        baseUnit: PropertyUnit,
        allCompsUnits: List<UnitListingData>,
    ): List<SimilarUnit> {
        val filteredUnits =
            allCompsUnits
                .let { units ->
                    units
                        .filter { it.unit.bedrooms == baseUnit.bedrooms && it.unit.bathrooms == baseUnit.bathrooms }
                        .takeIf { it.size >= unitSimilarityCalculatorConfig.minAmountUnitsToCompare } ?: units
                }.let { unitsWithSameUnitMix ->
                    unitsWithSameUnitMix
                        .takeIf { baseUnit.renovated != null }
                        ?.filter { it.unit.renovated == baseUnit.renovated }
                        ?.takeIf { it.size >= unitSimilarityCalculatorConfig.minAmountUnitsToCompare }
                        ?: unitsWithSameUnitMix
                }

        return filteredUnits.mapNotNull { compUnit ->
            calculateScore(baseUnit, compUnit.unit)
                .takeIf {
                    it >= unitSimilarityCalculatorConfig.similarityPercentageFilter
                }?.let { score ->
                    SimilarUnit(
                        unit = compUnit.unit,
                        listings = compUnit.listings,
                        score = score,
                    )
                }
        }
    }

    private fun calculateScore(
        baseUnit: PropertyUnit,
        compUnit: PropertyUnit,
    ): Double {
        val sqftScore =
            transformOptionalsOrDefault(baseUnit.squareFootage, compUnit.squareFootage, 0.0) { base, comp ->
                calculateSquareFootageScore(base.toDouble(), comp.toDouble())
            }

        val bedroomScore = calculateBedroomScore(baseUnit.bedrooms, compUnit.bedrooms)

        val bathroomScore =
            transformOptionalsOrDefault(baseUnit.bathrooms, compUnit.bathrooms, 0.0) { base, comp ->
                calculateBathroomScore(base.toDouble(), comp.toDouble())
            }

        val amenitiesScore = calculateAmenitiesSimilarity(baseUnit.amenities, compUnit.amenities)

        return (
            sqftScore * unitSimilarityCalculatorConfig.sqftWeight +
                bedroomScore * unitSimilarityCalculatorConfig.bedroomWeight +
                bathroomScore * unitSimilarityCalculatorConfig.bathroomWeight +
                amenitiesScore * unitSimilarityCalculatorConfig.amenitiesWeight
        )
    }

    private fun gaussianSimilarity(
        difference: Double,
        sigma: Double,
    ): Double = exp(-(difference.pow(2)) / (2 * sigma.pow(2)))

    private fun calculateSquareFootageScore(
        baseSqft: Double,
        compSqft: Double,
    ): Double {
        val difference = abs(baseSqft - compSqft)
        return gaussianSimilarity(difference, unitSimilarityCalculatorConfig.sqftSigma)
    }

    private fun calculateBathroomScore(
        baseBath: Double,
        compBath: Double,
    ): Double {
        val difference = abs(baseBath - compBath)
        return gaussianSimilarity(difference, unitSimilarityCalculatorConfig.bathroomSigma)
    }

    private fun calculateBedroomScore(
        baseBed: Int,
        compBed: Int,
    ): Double {
        val difference = abs(baseBed - compBed).toDouble()
        return gaussianSimilarity(difference, unitSimilarityCalculatorConfig.bedroomSigma)
    }

    private fun calculateAmenitiesSimilarity(
        propAmenities: Set<String>,
        compAmenities: Set<String>,
    ): Double {
        val unionElements = propAmenities.union(compAmenities).size
        if (unionElements == 0) return 0.0

        val intersectionElements = propAmenities.intersect(compAmenities).size
        val difference = 1.0 - intersectionElements.toDouble() / unionElements.toDouble()
        return gaussianSimilarity(difference, unitSimilarityCalculatorConfig.amenitiesSigma)
    }

    // In case we need to use the whole list including different reno states
    private fun calculateRenoSimilarity(
        baseReno: Boolean?,
        compReno: Boolean?,
    ): Double {
        if (baseReno != null && compReno != null && baseReno != compReno) {
            return unitSimilarityCalculatorConfig.renoSimilarity
        }
        return 0.0
    }

    private fun <T, R> transformOptionalsOrDefault(
        a: T?,
        b: T?,
        default: R,
        transform: (T, T) -> R,
    ): R =
        a?.let { aValue ->
            b?.let { bValue ->
                transform(aValue, bValue)
            }
        } ?: default
}
