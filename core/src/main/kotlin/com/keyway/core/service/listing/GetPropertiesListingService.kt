package com.keyway.core.service.listing

import com.keyway.core.dto.listings.input.GetPropertiesListingsInput
import com.keyway.core.dto.query.listings.PropertiesListingsQuery
import com.keyway.core.entities.RentListing
import com.keyway.core.ports.repositories.ListingsRepository
import com.keyway.core.utils.distributeEvenly
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.async
import kotlinx.coroutines.awaitAll
import kotlinx.coroutines.runBlocking
import kotlinx.coroutines.withContext

class GetPropertiesListingService(
    private val listingsRepository: ListingsRepository,
) {
    private fun runPartitioned(
        input: GetPropertiesListingsInput,
        repoCall: suspend (List<String>) -> List<RentListing>,
    ): List<RentListing> =
        input.propertyIds.distributeEvenly().let { partitionedIds ->
            runBlocking {
                withContext(Dispatchers.IO) {
                    val deferredResults =
                        partitionedIds.map { ids ->
                            async {
                                repoCall(ids)
                            }
                        }
                    deferredResults.awaitAll().flatten()
                }
            }
        }

    fun all(input: GetPropertiesListingsInput): List<RentListing> =
        runPartitioned(input) { ids ->
            listingsRepository
                .findListingsForProperties(buildQuery(input.copy(propertyIds = ids.toSet())))
        }

    fun last(input: GetPropertiesListingsInput): List<RentListing> =
        runPartitioned(input) { ids ->
            listingsRepository
                .findLastListingByProperties(buildQuery(input.copy(propertyIds = ids.toSet())))
        }

    private fun buildQuery(input: GetPropertiesListingsInput): PropertiesListingsQuery =
        PropertiesListingsQuery(
            propertyIds = input.propertyIds,
            typeId = null,
            type = null,
            floorPlan = null,
            dateFrom = input.dateFrom,
            dateTo = input.dateTo,
            bedrooms = input.bedrooms,
            bathrooms = input.bathrooms,
        )
}
