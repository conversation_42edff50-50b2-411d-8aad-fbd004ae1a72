package com.keyway.core.service.listing

import com.keyway.core.dto.listings.input.SaveRentGroupedListingInput
import com.keyway.core.dto.listings.input.UnitRecordsData
import com.keyway.core.entities.EffectiveRent
import com.keyway.core.entities.Money
import com.keyway.core.entities.RentListing
import com.keyway.core.entities.conciliation.ConciliationEffective
import com.keyway.core.entities.conciliation.ConciliationListing
import com.keyway.core.utils.DateUtils
import com.keyway.core.utils.DateUtils.isBetween
import com.keyway.core.utils.IdGenerator
import java.math.BigDecimal
import java.time.LocalDate

class GroupedListingService(
    private val idGenerator: IdGenerator,
) {
    private fun buildExistingListingsMap(
        existingListings: List<RentListing>,
        effectiveRentListings: List<EffectiveRent>,
    ): Map<LocalDate, ConciliationListing> {
        val effectiveRentMap = effectiveRentListings.groupBy { it.rentListingId }

        return existingListings
            .flatMap { listing ->
                DateUtils.generateDateRange(listing.dateFrom, listing.dateTo).map { date ->
                    date to
                        createListingData(
                            listing,
                            date,
                            effectiveRentMap[listing.id]?.firstOrNull { date.isBetween(it.dateFrom, it.dateTo) },
                        )
                }
            }.toMap()
    }

    private fun getLastThreeAlphanumeric(input: String): String {
        // Filter only letters and numbers, then take the last 3
        val alphanumeric = input.filter { it.isLetterOrDigit() }
        return if (alphanumeric.length >= 3) {
            alphanumeric.takeLast(3)
        } else {
            alphanumeric // Return all available characters if less than 3
        }
    }

    fun buildId(
        propertyId: String,
        typeId: String,
    ) = "${propertyId.split("-").last()}-${getLastThreeAlphanumeric(typeId)}-${idGenerator.invoke()}"

    fun processGroupedListing(
        input: SaveRentGroupedListingInput,
        existingListings: List<RentListing>,
        effectiveRentListings: List<EffectiveRent>,
        incomingSortedRecords: List<UnitRecordsData>,
    ): Pair<List<RentListing>, List<EffectiveRent>> {
        val existingListingsMap = buildExistingListingsMap(existingListings, effectiveRentListings)
        val incomingListingsMap = buildIncomingListingsMap(incomingSortedRecords, input.propertyId, input.typeId)

        val (lowerLimit, higherLimit) = calculateSequenceLimits(existingListings, incomingSortedRecords)

        val conciliatedMap = conciliateListings(lowerLimit, higherLimit, existingListingsMap, incomingListingsMap)

        return generateListings(conciliatedMap, input)
    }

    private fun generateListings(
        conciliatedMap: Map<LocalDate, ConciliationListing>,
        input: SaveRentGroupedListingInput,
    ): Pair<List<RentListing>, List<EffectiveRent>> {
        val effectiveRents = mutableListOf<EffectiveRent>()
        return conciliatedMap.values
            .groupBy { it.id }
            .map { (id, listings) ->
                val conciliationElement = generateConciliationElement(listings)

                val maxDate = listings.maxBy { it.date }.date

                createRentListing(id = id!!, dateFrom = conciliationElement.date, dateTo = maxDate, input = input, conciliationElement = conciliationElement)
                    .also {
                        effectiveRents.addAll(buildEffectiveRentList(it, listings))
                    }
            } to effectiveRents
    }

    private fun buildEffectiveRentList(
        rentListing: RentListing,
        listings: List<ConciliationListing>,
    ): List<EffectiveRent> =
        listings
            .groupBy { it.effective.id }
            .map { (id, effectiveList) ->
                val effectiveData = effectiveList.first().effective
                EffectiveRent.build(
                    id = id!!,
                    listing = rentListing,
                    concessionIds = emptyList(),
                    dateFrom = effectiveList.minBy { it.date }.date,
                    dateTo = effectiveList.maxBy { it.date }.date,
                    rent = getRentValue(effectiveData.effectiveRent, rentListing.rent),
                    rentDeposit = getDepositValue(effectiveData.effectiveDeposit, rentListing.rentDeposit),
                    concessions = effectiveData.concessions ?: "",
                    createdAt = effectiveData.createdAt,
                    updateAt = DateUtils.now(),
                )
            }

    private fun generateConciliationElement(listings: List<ConciliationListing>): ConciliationListing {
        val listingElement = listings.minBy { it.date }

        val availableIn: LocalDate? =
            listings
                .mapNotNull { it.availableIn }
                .maxOrNull()

        val maxDeposit: Money? =
            listings
                .mapNotNull { it.rentDeposit }
                .maxOrNull()

        val maxEffectiveDeposit: Money? =
            listings
                .mapNotNull { it.effective.effectiveDeposit }
                .maxOrNull()

        return listingElement.copy(
            availableIn = availableIn,
            rentDeposit = maxDeposit,
            conciliationEffective = listingElement.effective.copy(effectiveDeposit = maxEffectiveDeposit),
        )
    }

    private fun calculateSequenceLimits(
        existingListings: List<RentListing>,
        sortedRecords: List<UnitRecordsData>,
    ): Pair<LocalDate, LocalDate> =
        listOfNotNull(
            existingListings.firstOrNull()?.dateFrom,
            existingListings.lastOrNull()?.dateTo,
            sortedRecords.first().recordDate,
            sortedRecords.last().recordDate,
        ).let { it.min() to it.max() }

    private fun createListingData(
        listing: RentListing,
        date: LocalDate,
        effectiveRent: EffectiveRent?,
    ): ConciliationListing =
        ConciliationListing(
            id = listing.id,
            propertyId = listing.propertyId,
            typeId = listing.typeId,
            date = date,
            recordSource = listing.recordSource,
            rent = listing.rent,
            rentDeposit = listing.rentDeposit,
            effective =
                ConciliationEffective(
                    id = effectiveRent?.id,
                    recordDate = date,
                    concessions = effectiveRent?.concessions,
                    effectiveRent = effectiveRent?.rent ?: Money.zero(),
                    effectiveDeposit = effectiveRent?.rentDeposit ?: listing.rentDeposit,
                    createdAt = effectiveRent?.createdAt ?: DateUtils.now(),
                ),
            availableIn = listing.availableIn,
            createdAt = listing.createdAt,
        )

    private fun conciliateListings(
        lowerLimit: LocalDate,
        higherLimit: LocalDate,
        existingListingsMap: Map<LocalDate, ConciliationListing>,
        incomingListingsMap: Map<LocalDate, ConciliationListing>,
    ): Map<LocalDate, ConciliationListing> {
        val conciliatedListingsMap = mutableMapOf<LocalDate, ConciliationListing>()
        var lastProcessedListing: ConciliationListing? = null
        DateUtils
            .generateDateRange(lowerLimit, higherLimit)
            .forEach { date ->
                val existingListing = existingListingsMap[date]
                val incomingListing = incomingListingsMap[date]

                val conciliatedListing = selectListing(existingListing, incomingListing, lastProcessedListing)

                if (conciliatedListing != null) {
                    conciliatedListingsMap[date] = conciliatedListing
                    lastProcessedListing = conciliatedListing
                }
            }
        return conciliatedListingsMap
    }

    private fun selectListing(
        existingListing: ConciliationListing?,
        incomingListing: ConciliationListing?,
        lastProcessedListing: ConciliationListing?,
    ): ConciliationListing? {
        if (existingListing != null && incomingListing != null) {
            return createConciliationListing(existingListing, incomingListing, lastProcessedListing)
        }

        if (incomingListing == null && existingListing == null) return null

        return (incomingListing ?: existingListing)!!.let { listing ->
            listing
                .copy(
                    id =
                        getListingData(
                            lastProcessedListing,
                            listing,
                            existingListing != null,
                        )?.id
                            ?: buildId(listing.propertyId, listing.typeId),
                    createdAt =
                        getListingData(
                            lastProcessedListing,
                            listing,
                            existingListing != null,
                        )?.createdAt ?: DateUtils.now(),
                ).let { selectedListing ->
                    selectedListing.copy(
                        conciliationEffective =
                            selectedListing.effective.copy(
                                id =
                                    getEffectiveData(
                                        lastProcessedListing,
                                        selectedListing,
                                        existingListing != null,
                                    )?.id
                                        ?: buildId(selectedListing.propertyId, selectedListing.typeId),
                            ),
                    )
                }
        }
    }

    private fun getListingData(
        lastProcessedListing: ConciliationListing?,
        listing: ConciliationListing,
        isExistingData: Boolean,
    ): ConciliationListing? =
        when {
            lastProcessedListing?.id == null && listing.id != null -> listing
            lastProcessedListing?.id != null && lastProcessedListing == listing -> lastProcessedListing
            isExistingData && listing.id != null -> listing
            else -> null
        }

    private fun getEffectiveData(
        lastProcessed: ConciliationListing?,
        selected: ConciliationListing,
        isExistingData: Boolean,
    ): ConciliationEffective? =
        when {
            lastProcessed?.effective?.id == null && selected.effective.id != null -> selected.effective
            lastProcessed?.effective?.id != null &&
                lastProcessed.effective == selected.effective &&
                lastProcessed.id == selected.id -> lastProcessed.effective
            isExistingData && selected.effective.id != null -> selected.effective
            else -> null
        }

    private fun createConciliationListing(
        existingElement: ConciliationListing,
        incomingElement: ConciliationListing,
        lastProcessedListing: ConciliationListing?,
    ): ConciliationListing =
        when {
            lastProcessedListing == incomingElement || existingElement != incomingElement ->
                incomingElement.copy(
                    id =
                        getListingData(lastProcessedListing, incomingElement, false)?.id
                            ?: buildId(incomingElement.propertyId, incomingElement.typeId),
                    createdAt = getListingData(lastProcessedListing, incomingElement, false)?.createdAt ?: DateUtils.now(),
                )
            else -> existingElement
        }.let { selectedListing ->
            selectedListing.copy(
                conciliationEffective =
                    when {
                        lastProcessedListing?.effective == incomingElement.effective ||
                            existingElement.effective != incomingElement.effective ->
                            incomingElement.effective.copy(
                                id =
                                    getEffectiveData(
                                        lastProcessedListing,
                                        selectedListing.copy(conciliationEffective = incomingElement.effective),
                                        false,
                                    )?.id
                                        ?: buildId(selectedListing.propertyId, selectedListing.typeId),
                                createdAt =
                                    getEffectiveData(
                                        lastProcessedListing,
                                        selectedListing.copy(conciliationEffective = incomingElement.effective),
                                        false,
                                    )?.createdAt
                                        ?: DateUtils.now(),
                            )
                        else -> existingElement.effective
                    },
            )
        }

    private fun buildIncomingListingsMap(
        sortedRecords: List<UnitRecordsData>,
        propertyId: String,
        typeId: String,
    ) = sortedRecords.associate { listingData ->
        listingData.recordDate to
            ConciliationListing(
                id = null,
                date = listingData.recordDate,
                recordSource = listingData.recordSource,
                rent = Money.of(listingData.rent),
                rentDeposit = listingData.rentDeposit?.let { deposit -> Money.of(deposit) },
                effective =
                    ConciliationEffective(
                        id = null,
                        recordDate = listingData.recordDate,
                        concessions = listingData.concessions,
                        effectiveRent = Money.of(listingData.effectiveRent),
                        effectiveDeposit = listingData.effectiveRentDeposit?.let { Money.of(it) },
                        createdAt = DateUtils.now(),
                    ),
                availableIn = listingData.availableIn,
                createdAt = DateUtils.now(),
                propertyId = propertyId,
                typeId = typeId,
            )
    }

    private fun createRentListing(
        id: String,
        dateFrom: LocalDate,
        dateTo: LocalDate,
        input: SaveRentGroupedListingInput,
        conciliationElement: ConciliationListing,
    ) = RentListing(
        id = id,
        propertyId = input.propertyId,
        type = input.type,
        typeId = input.typeId,
        dateFrom = dateFrom,
        dateTo = dateTo,
        recordSource = conciliationElement.recordSource,
        zipCode = input.zipCode,
        msaCode = input.msaCode,
        unitSquareFootage = input.unitSquareFootage,
        bedroomsQuantity = input.bedroomsQuantity,
        bathroomsQuantity = input.bathroomsQuantity,
        floorPlan = input.floorPlan,
        availableIn = conciliationElement.availableIn?.takeIf { it.isAfter(DateUtils.now()) },
        rent = conciliationElement.rent,
        rentDeposit = conciliationElement.rentDeposit,
        createdAt = conciliationElement.createdAt,
        updateAt = DateUtils.now(),
    )

    private fun getRentValue(
        effectiveValue: Money?,
        inputValue: Money,
    ): Money =
        effectiveValue?.takeIf { it.value > BigDecimal.ZERO }
            ?: inputValue

    private fun getDepositValue(
        effectiveValue: Money?,
        inputValue: Money?,
    ): Money? =
        effectiveValue?.takeIf { it.value > BigDecimal.ZERO }
            ?: inputValue
}
