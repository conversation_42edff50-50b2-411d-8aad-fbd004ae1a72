package com.keyway.core.dto.listings.output

import com.keyway.core.dto.listings.input.AggregatedMetricType
import java.math.BigDecimal
import java.time.LocalDate

sealed interface RentMetricOutput {
    val id: String
    val dateFrom: LocalDate
    val dateTo: LocalDate
    val metrics: List<MetricDataOutput>
    val type: AggregatedMetricType
}

sealed interface MetricDataOutput {
    val askingRent: MetricDetailOutput
    val askingRentPSF: MetricDetailOutput?
    val effectiveRent: MetricDetailOutput
    val effectiveRentPSF: MetricDetailOutput?
    val deposit: MetricDetailOutput?
    val recordsQuantity: Int
    val averageListingDays: BigDecimal
    val unitsAvailable: Int
    val totalUnits: Int
}

data class MetricDetailOutput(
    val min: BigDecimal,
    val max: BigDecimal,
    val average: BigDecimal,
    val median: BigDecimal,
)

data class ByIdRentMetricOutput(
    val squareFootage: MetricDetailOutput?,
    override val askingRent: MetricDetailOutput,
    override val askingRentPSF: MetricDetailOutput?,
    override val effectiveRent: MetricDetailOutput,
    override val effectiveRentPSF: MetricDetailOutput?,
    override val deposit: MetricDetailOutput?,
    override val recordsQuantity: Int,
    override val averageListingDays: BigDecimal,
    override val unitsAvailable: Int,
    override val totalUnits: Int,
) : MetricDataOutput

data class ByIdMetricsOutput(
    override val id: String,
    override val dateFrom: LocalDate,
    override val dateTo: LocalDate,
    override val metrics: List<ByIdRentMetricOutput>,
    override val type: AggregatedMetricType = AggregatedMetricType.BY_ID,
) : RentMetricOutput

data class UnitMixRentMetricsOutput(
    val bedrooms: Int,
    val bathrooms: BigDecimal,
    val squareFootage: MetricDetailOutput?,
    override val askingRent: MetricDetailOutput,
    override val askingRentPSF: MetricDetailOutput?,
    override val effectiveRent: MetricDetailOutput,
    override val effectiveRentPSF: MetricDetailOutput?,
    override val deposit: MetricDetailOutput?,
    override val recordsQuantity: Int,
    override val averageListingDays: BigDecimal,
    override val unitsAvailable: Int,
    override val totalUnits: Int,
) : MetricDataOutput

data class UnitMixMetricsOutput(
    override val id: String,
    override val dateFrom: LocalDate,
    override val dateTo: LocalDate,
    override val metrics: List<UnitMixRentMetricsOutput>,
    override val type: AggregatedMetricType = AggregatedMetricType.UNIT_MIX,
) : RentMetricOutput

data class FloorPlanRentMetricOutput(
    val floorPlan: String,
    val bedrooms: Int,
    val bathrooms: BigDecimal,
    val squareFootage: MetricDetailOutput?,
    override val askingRent: MetricDetailOutput,
    override val askingRentPSF: MetricDetailOutput?,
    override val effectiveRent: MetricDetailOutput,
    override val effectiveRentPSF: MetricDetailOutput?,
    override val deposit: MetricDetailOutput?,
    override val recordsQuantity: Int,
    override val averageListingDays: BigDecimal,
    override val unitsAvailable: Int,
    override val totalUnits: Int,
) : MetricDataOutput

data class FloorPlanMetricsOutput(
    override val id: String,
    override val dateFrom: LocalDate,
    override val dateTo: LocalDate,
    override val metrics: List<FloorPlanRentMetricOutput>,
    override val type: AggregatedMetricType = AggregatedMetricType.FLOOR_PLAN,
) : RentMetricOutput

data class UnitsRentMetricOutput(
    val unitId: String,
    val floorPlan: String,
    val bedrooms: Int,
    val bathrooms: BigDecimal,
    val squareFootage: BigDecimal?,
    override val askingRent: MetricDetailOutput,
    override val askingRentPSF: MetricDetailOutput?,
    override val effectiveRent: MetricDetailOutput,
    override val effectiveRentPSF: MetricDetailOutput?,
    override val deposit: MetricDetailOutput?,
    override val recordsQuantity: Int,
    override val averageListingDays: BigDecimal,
    override val unitsAvailable: Int,
    override val totalUnits: Int,
) : MetricDataOutput

data class UnitsMetricsOutput(
    override val id: String,
    override val dateFrom: LocalDate,
    override val dateTo: LocalDate,
    override val metrics: List<UnitsRentMetricOutput>,
    override val type: AggregatedMetricType = AggregatedMetricType.UNITS,
) : RentMetricOutput

data class BedroomRentMetricOutput(
    val bedrooms: Int,
    val squareFootage: MetricDetailOutput?,
    override val askingRent: MetricDetailOutput,
    override val askingRentPSF: MetricDetailOutput?,
    override val effectiveRent: MetricDetailOutput,
    override val effectiveRentPSF: MetricDetailOutput?,
    override val deposit: MetricDetailOutput?,
    override val recordsQuantity: Int,
    override val averageListingDays: BigDecimal,
    override val unitsAvailable: Int,
    override val totalUnits: Int,
) : MetricDataOutput

data class BedroomMetricsOutput(
    override val id: String,
    override val dateFrom: LocalDate,
    override val dateTo: LocalDate,
    override val metrics: List<BedroomRentMetricOutput>,
    override val type: AggregatedMetricType = AggregatedMetricType.BEDROOMS,
) : RentMetricOutput
