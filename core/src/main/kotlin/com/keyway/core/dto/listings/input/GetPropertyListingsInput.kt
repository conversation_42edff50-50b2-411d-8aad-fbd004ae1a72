package com.keyway.core.dto.listings.input

import com.keyway.core.entities.RentListingType
import java.math.BigDecimal
import java.time.LocalDate

data class GetPropertyListingsInput(
    val propertyId: String,
    val unitId: String?,
    val type: RentListingType?,
    val floorPlan: String?,
    val dateFrom: LocalDate?,
    val dateTo: LocalDate?,
    val bedrooms: Int?,
    val bathrooms: BigDecimal?,
)
