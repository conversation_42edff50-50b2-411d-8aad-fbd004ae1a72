package com.keyway.core.dto.query.metrics

import com.keyway.core.entities.RentListingType
import com.keyway.core.entities.UnitCondition
import com.keyway.core.entities.metric.MetricType
import java.time.LocalDate

data class MetricsFiltersQuery(
    val ids: Set<String>,
    val idType: IdType,
    val dateFrom: LocalDate,
    val dateTo: LocalDate,
    val type: MetricType,
    val rentListingType: RentListingType? = null,
    val unitCondition: UnitCondition? = null,
)

enum class IdType {
    PROPERTY,
    MSA,
    ZIP_CODE,
}
