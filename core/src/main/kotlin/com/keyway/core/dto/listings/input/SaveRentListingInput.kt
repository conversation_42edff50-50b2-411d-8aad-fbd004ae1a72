package com.keyway.core.dto.listings.input

import com.keyway.core.entities.RentListingType
import java.math.BigDecimal
import java.time.LocalDate

data class UnitRecordsData(
    val recordSource: String,
    val rent: BigDecimal,
    val rentDeposit: BigDecimal?,
    val effectiveRent: BigDecimal,
    val effectiveRentDeposit: BigDecimal?,
    val availableIn: LocalDate?,
    val recordDate: LocalDate,
    val concessions: String?,
)

data class SaveRentGroupedListingInput(
    val propertyId: String,
    val type: RentListingType = RentListingType.UNIT,
    val typeId: String,
    val zipCode: String,
    val msaCode: String,
    val bedroomsQuantity: Int,
    val bathroomsQuantity: BigDecimal?,
    val floorPlan: String?,
    val unitSquareFootage: BigDecimal?,
    val records: List<UnitRecordsData>,
)
