package com.keyway.core.dto

import com.keyway.core.dto.query.metrics.IdType
import java.math.BigDecimal

data class SummarizedMetricDto(
    val id: String,
    val metricType: IdType,
    val totalRecords: Int,
    val averageListingsDays: BigDecimal,
    val bedrooms: Int?,
    val totalUnits: Int,
    val askingRent: GeoMetric,
    val effectiveRent: GeoMetric,
    val squareFootage: GeoMetric,
    val totalProperties: Int,
) {
    constructor(asking: GeoAskingMetricDto, effective: GeoEffectiveMetricDto?) : this(
        id = asking.id,
        metricType = asking.idType,
        totalRecords = asking.totalRecords,
        averageListingsDays = asking.averageListingsDays,
        totalUnits = asking.totalUnits,
        askingRent = asking.askingRent,
        bedrooms = asking.bedrooms,
        effectiveRent = effective?.effectiveRent ?: asking.askingRent,
        squareFootage = asking.squareFootage,
        totalProperties = asking.totalProperties,
    )
}

data class GeoEffectiveMetricDto(
    val id: String,
    val idType: String,
    val effectiveRent: GeoMetric?,
    val bedrooms: Int?,
) {
    val code = "$id//$bedrooms"
}

data class GeoAskingMetricDto(
    val id: String,
    val idType: IdType,
    val totalRecords: Int,
    val averageListingsDays: BigDecimal,
    val askingRent: GeoMetric,
    val bedrooms: Int?,
    val squareFootage: GeoMetric,
    val totalUnits: Int,
    val totalProperties: Int,
) {
    val code = "$id//$idType"
}

data class GeoMetric(
    val min: BigDecimal,
    val max: BigDecimal,
    val average: BigDecimal,
)
