package com.keyway.core.dto.listings.output

import java.math.BigDecimal

data class CalculateRentSuggestionOutput(
    val propertyId: String,
    val units: List<UnitRentSuggestionOutput>,
)

data class UnitRentSuggestionOutput(
    val unitId: String,
    val askingRent: BigDecimal,
    val effectiveRent: BigDecimal,
    val similarUnitsComparisons: List<UnitComparison>,
)

data class UnitComparison(
    val propertyId: String,
    val unitId: String,
    val similarityScore: Double,
)
