package com.keyway.core.dto.concessions.input

import java.math.BigDecimal
import java.time.LocalDate

data class SaveConcessionInput(
    val propertyId: String,
    val records: List<ConcessionRecordInput>,
)

data class ConcessionRecordInput(
    val concessionText: String,
    val benefits: List<ConcessionBenefitInput>,
    val recordDate: LocalDate,
)

data class ConcessionBenefitInput(
    val type: String?,
    val benefit: String?,
    val deadline: LocalDate?,
    val amountType: String?,
    val amountValue: BigDecimal?,
    val periodicityDuration: String?,
    val periodicityAmount: String?,
    val periodicityRecurrent: Boolean?,
    val requirements: String?,
)
