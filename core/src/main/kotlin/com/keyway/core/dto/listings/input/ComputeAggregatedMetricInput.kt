package com.keyway.core.dto.listings.input

import com.keyway.core.dto.query.metrics.IdType
import com.keyway.core.entities.UnitCondition
import java.time.LocalDate

enum class AggregatedMetricType {
    BY_ID,
    FLOOR_PLAN,
    UNIT_MIX,
    BEDROOMS,
    UNITS,
}

data class ComputeAggregatedMetricInput(
    val ids: Set<String>,
    val idType: IdType,
    val dateFrom: LocalDate?,
    val dateTo: LocalDate?,
    val type: AggregatedMetricType,
    val unitCondition: UnitCondition?,
)

data class ComputeAggregatedGeoMetricInput(
    val ids: Set<String>,
    val idType: IdType,
    val dateFrom: LocalDate?,
    val dateTo: LocalDate?,
    val type: AggregatedMetricType,
)
