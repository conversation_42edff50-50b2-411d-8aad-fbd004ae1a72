package com.keyway.core.dto

import java.math.BigDecimal

data class Metric(
    val min: BigDecimal,
    val max: BigDecimal,
    val average: BigDecimal,
    val median: BigDecimal,
)

data class MetricDto(
    val id: String,
    val totalRecords: Int,
    val averageListingsDays: BigDecimal,
    val unitsAvailable: Int,
    val askingRent: Metric,
    val effectiveRent: Metric,
    val deposit: Metric?,
    val squareFootage: Metric?,
    val bedrooms: Int?,
    val bathrooms: BigDecimal?,
    val floorPlan: String?,
    val totalUnits: Int,
    val unitId: String?,
) {
    constructor(asking: AskingMetricDto, effective: EffectiveMetricDto?) : this(
        id = asking.id,
        totalRecords = asking.totalRecords,
        averageListingsDays = asking.averageListingsDays,
        unitsAvailable = asking.unitsAvailable,
        askingRent = asking.askingRent,
        effectiveRent = effective?.effectiveRent ?: asking.askingRent,
        deposit = asking.deposit,
        squareFootage = asking.squareFootage,
        bedrooms = asking.bedrooms,
        bathrooms = asking.bathrooms,
        floorPlan = asking.floorPlan,
        totalUnits = asking.totalUnits,
        unitId = asking.unitId,
    )
}

data class AskingMetricDto(
    val id: String,
    val totalRecords: Int,
    val averageListingsDays: BigDecimal,
    val unitsAvailable: Int,
    val askingRent: Metric,
    val deposit: Metric?,
    val squareFootage: Metric?,
    val bedrooms: Int?,
    val bathrooms: BigDecimal?,
    val floorPlan: String?,
    val totalUnits: Int,
    val unitId: String?,
) {
    val code = "$id//$bedrooms//$bathrooms//$floorPlan"
}

data class EffectiveMetricDto(
    val id: String,
    val effectiveRent: Metric?,
    val bedrooms: Int?,
    val bathrooms: BigDecimal?,
    val floorPlan: String?,
) {
    val code = "$id//$bedrooms//$bathrooms//$floorPlan"
}
