package com.keyway.core.dto.query.listings

import com.keyway.core.entities.RentListingType
import java.math.BigDecimal
import java.time.LocalDate

data class PropertiesListingsQuery(
    val propertyIds: Set<String>,
    val type: RentListingType? = RentListingType.UNIT,
    val typeId: String?,
    val floorPlan: String?,
    val dateFrom: LocalDate?,
    val dateTo: LocalDate?,
    val bedrooms: Int?,
    val bathrooms: BigDecimal?,
    val typeIds: Set<String> = emptySet(),
)
