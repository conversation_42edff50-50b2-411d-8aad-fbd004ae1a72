package com.keyway.core.entities

import java.math.BigDecimal
import java.time.LocalDate

data class MultifamilyProperty(
    val id: String,
    val address: String,
    val city: String,
    val county: String?,
    val zipCode: Long,
    val state: String,
    val location: GeoPoint,
    val geolocation: String,
    val squareFootage: BigDecimal?,
    val squareFootagePerUnit: BigDecimal?,
    val sourceType: String? = null,
    val tractCode: Long?,
    val constructionYear: Int?,
    val renovationYear: Int?,
    val unitQuantity: Int,
    val occupancyPercentage: BigDecimal?,
    val propertyAmenities: Set<String> = emptySet(),
    val unitsAmenities: Set<String> = emptySet(),
    val isActive: Boolean,
    val qualityOverallScore: BigDecimal? = null,
    val lastSeen: LocalDate? = null,
    val stories: Int? = null,
    val propertyStyle: String? = null,
    val landSizeSqft: BigDecimal? = null,
    val housingSegment: Set<String> = emptySet(),
    val hasAffordableUnits: Boolean? = null,
)
