package com.keyway.core.entities.concessions

import java.math.BigDecimal
import java.time.LocalDate

data class PropertyConcessionBenefit(
    /** The number of free months. If the benefit is a week take into account that there are 4 weeks in a month. */
    val freeMonthsAmount: BigDecimal? = null,
    /** The date until the free months */
    val freeMonthsUntil: LocalDate? = null,
    /** The amount of the one time dollars off */
    val oneTimeDollarsOffAmount: BigDecimal? = null,
    /** The percentage of the one time dollars off */
    val oneTimeDollarsOffPercentage: BigDecimal? = null,
    /** The amount of the recurring dollars off */
    val recurringDollarsOffAmount: BigDecimal? = null,
    /** The percentage of the recurring dollars off. Example: 10% */
    val recurringDollarsOffPercentage: BigDecimal? = null,
    /** The number of months the recurring dollars off is applied */
    val recurringMonthsTerm: Int? = null,
    /** The lease term months */
    val leaseTermMonths: List<Int>? = null,
    /** The deadline of the lease term */
    val conditionDeadline: LocalDate? = null,
    /** The unit type bedrooms that are eligible for the benefit */
    val conditionBedrooms: List<Int>? = null,
    /** The unit names that are eligible for the benefit */
    val conditionUnitNames: List<String>? = null,
    /** The floorplans that are eligible for the benefit */
    val conditionFloorplans: List<String>? = null,
    /** If the benefit applies to a selected units */
    val conditionSelectedUnits: Boolean? = null,
    /** If the benefit applies to a selected floorplans */
    val conditionSelectedFloorplans: Boolean? = null,
    /** If the benefit applies to a selected employees */
    val conditionSelectedEmployees: Boolean? = null,
    /** If the application fee is waived */
    val waivedApplicationFee: Boolean? = null,
    /** If the security deposit is waived */
    val waivedSecurityDeposit: Boolean? = null,
    /** If the administrative fee is waived */
    val waivedAdministrativeFee: Boolean? = null,
    /** If the move-in fee is waived */
    val waivedMoveInFee: Boolean? = null,
    /** If the security deposit is cheaper */
    val cheaperSecurityDeposit: Boolean? = null,
    /** If the administrative fee is cheaper */
    val cheaperAdministrativeFee: Boolean? = null,
    /** If the application fee is cheaper */
    val cheaperApplicationFee: Boolean? = null,
    /** If the move-in fee is cheaper */
    val cheaperMoveInFee: Boolean? = null,
    /** If the rent is cheaper */
    val cheaperRent: Boolean? = null,
    /** The group id of the benefit */
    val benefitGroupId: String? = null,
)
