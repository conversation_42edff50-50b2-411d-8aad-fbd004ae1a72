package com.keyway.core.entities

import java.math.BigDecimal
import java.time.LocalDate
import java.time.OffsetDateTime

data class RentListing(
    val id: String,
    val propertyId: String,
    val type: RentListingType,
    val typeId: String,
    val rent: Money,
    val dateFrom: LocalDate,
    val dateTo: LocalDate,
    val recordSource: String,
    val zipCode: String,
    val msaCode: String,
    val unitSquareFootage: BigDecimal?,
    val bedroomsQuantity: Int,
    val bathroomsQuantity: BigDecimal?,
    val floorPlan: String?,
    val availableIn: LocalDate?,
    val rentDeposit: Money?,
    val createdAt: OffsetDateTime,
    val updateAt: OffsetDateTime,
    val isActive: Boolean = true,
) {
    override fun equals(other: Any?): Boolean {
        if (this === other) return true
        if (javaClass != other?.javaClass) return false

        other as RentListing

        if (id != other.id) return false
        if (propertyId != other.propertyId) return false
        if (type != other.type) return false
        if (typeId != other.typeId) return false
        if (rent != other.rent) return false
        if (dateFrom != other.dateFrom) return false
        if (dateTo != other.dateTo) return false
        if (recordSource != other.recordSource) return false
        if (zipCode != other.zipCode) return false
        if (msaCode != other.msaCode) return false
        if (unitSquareFootage != other.unitSquareFootage) return false
        if (bedroomsQuantity != other.bedroomsQuantity) return false
        if (bathroomsQuantity != other.bathroomsQuantity) return false
        if (floorPlan != other.floorPlan) return false
        if (availableIn != other.availableIn) return false
        if (rentDeposit != other.rentDeposit) return false
        if (isActive != other.isActive) return false

        return true
    }

    override fun hashCode(): Int = super.hashCode()

    override fun toString(): String = "RentListing(id='$id', propertyId='$propertyId', $type='$typeId', date=$dateFrom-$dateTo, rent=${rent.value}, isActive=$isActive)"
}
