package com.keyway.core.entities.concessions

import com.keyway.core.utils.DateUtils
import com.keyway.core.utils.DateUtils.isBetween
import java.time.LocalDate
import java.time.OffsetDateTime

data class PropertyConcessionV2(
    val id: String,
    val propertyId: String,
    val zipCode: String?,
    val msaCode: String?,
    val concessionText: String,
    val benefits: List<PropertyConcessionBenefit>,
    val dateFrom: LocalDate,
    val dateTo: LocalDate,
    val createdAt: OffsetDateTime,
    val updatedAt: OffsetDateTime,
) {
    companion object {
        private const val ACTIVE_AMOUNT_DAYS = 3L
    }

    var active = shouldBeActive()

    fun shouldBeActive(): Boolean =
        DateUtils.now<LocalDate>().isBetween(
            dateFrom,
            dateTo.plusDays(ACTIVE_AMOUNT_DAYS),
        )
}
