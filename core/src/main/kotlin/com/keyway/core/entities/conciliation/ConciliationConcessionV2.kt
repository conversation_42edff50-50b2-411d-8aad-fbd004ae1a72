package com.keyway.core.entities.conciliation

import com.keyway.core.entities.concessions.PropertyConcessionBenefit
import java.time.LocalDate
import java.time.OffsetDateTime

data class ConciliationConcessionV2(
    val id: String,
    val propertyId: String,
    val zipCode: String?,
    val msaCode: String?,
    val concessionText: String,
    val benefits: List<PropertyConcessionBenefit>,
    val date: LocalDate,
    val createdAt: OffsetDateTime,
    val updatedAt: OffsetDateTime,
) {
    fun isEquivalentTo(other: ConciliationConcessionV2?): Boolean = other != null && this.concessionText == other.concessionText && other.date.plusDays(7).isAfter(this.date)
}
