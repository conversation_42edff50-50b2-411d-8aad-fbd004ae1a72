package com.keyway.core.entities.concessions

fun String.normalizeEnumValue(): String = this.replace(" ", "_").replace("-", "_").uppercase()

enum class BenefitType {
    ADMINISTRATION,
    APPLICATION,
    MOVE_IN,
    RENT,
    SECURITY,
    DEPOSIT,
    GIFT_CARD,
    ;

    companion object {
        fun optionalValueOf(input: String?) = input?.let { entries.firstOrNull { it.name == input.normalizeEnumValue() } }
    }
}

enum class ConcessionPeriodicityDuration {
    DAYS,
    WEEKS,
    MONTHS,
    YEARS,
    ;

    companion object {
        fun optionalValueOf(input: String?) = input?.let { entries.firstOrNull { it.name == input.normalizeEnumValue() } }
    }
}

enum class ConcessionBenefitCategory {
    ADMINISTRATION,
    APPLICATION,
    MOVE_IN,
    RENT,
    SECURITY_DEPOSIT,
    GIFT_CARD,
    ;

    companion object {
        fun optionalValueOf(input: String?) = input?.let { entries.firstOrNull { it.name == input.normalizeEnumValue() } }
    }
}

enum class ConcessionBenefitAmountType {
    FIXED,
    PERCENTAGE,
    ;

    companion object {
        fun optionalValueOf(input: String?) = input?.let { entries.firstOrNull { it.name == input.normalizeEnumValue() } }
    }
}

enum class ConcessionBenefitType {
    FREE,
    DISCOUNT,
    PRO_RATED,
    ;

    companion object {
        fun optionalValueOf(input: String?) = input?.let { entries.firstOrNull { it.name == input.normalizeEnumValue() } }
    }
}

enum class ConcessionBenefitTargetType {
    UNIT,
    PROPERTY,
    FLOOR_PLAN,
}
