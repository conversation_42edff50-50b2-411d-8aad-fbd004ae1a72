package com.keyway.core.entities

import java.math.BigDecimal
import java.math.MathContext
import java.math.RoundingMode

data class Money private constructor(
    val value: BigDecimal,
    private val context: MathContext = mathContext,
) : Comparable<Money> {
    companion object {
        val mathContext: MathContext = MathContext.DECIMAL64

        fun zero(): Money = Money(BigDecimal.ZERO)

        fun of(value: BigDecimal): Money = Money(value = BigDecimal(value.toString(), mathContext).setScale(2, RoundingMode.HALF_UP), context = mathContext)

        fun of(value: String): Money = Money(value = BigDecimal(value, mathContext).setScale(2, RoundingMode.HALF_UP), context = mathContext)

        fun of(value: Int): Money = Money(value = BigDecimal(value, mathContext).setScale(2, RoundingMode.HALF_UP), context = mathContext)

        fun of(value: Double): Money = Money(value = BigDecimal(value, mathContext).setScale(2, RoundingMode.HALF_UP), context = mathContext)
    }

    fun divide(divisor: Money): Money = this.value.divide(divisor.value, mathContext).let(::Money)

    fun add(augend: Money): Money = this.value.add(augend.value).let(::Money)

    fun subtract(subtrahend: Money): Money = this.value.subtract(subtrahend.value).let(::Money)

    fun multiply(multiplicand: Money): Money = this.value.multiply(multiplicand.value, mathContext).let(::Money)

    operator fun plus(other: Money): Money = this.add(other)

    operator fun minus(other: Money): Money = this.subtract(other)

    operator fun times(other: Money): Money = this.multiply(other)

    operator fun times(times: Int): Money = this.multiply(Money(times.toBigDecimal()))

    operator fun div(other: Money): Money = this.divide(other)

    override fun compareTo(other: Money): Int = this.value.compareTo(other.value)

    override fun equals(other: Any?): Boolean =
        when (other) {
            is Money -> this.value == other.value
            else -> this.value == other
        }

    override fun hashCode(): Int = value.hashCode()

    fun copy(): Money = of(this.value)
}

fun Iterable<Money>.sum(): Money = this.fold(Money.zero()) { a, b -> a + b }

inline fun <T> Iterable<T>.sumOf(selector: (T) -> Money): Money {
    var sum: Money = Money.zero()
    for (element in this) {
        sum += selector(element)
    }
    return sum
}
