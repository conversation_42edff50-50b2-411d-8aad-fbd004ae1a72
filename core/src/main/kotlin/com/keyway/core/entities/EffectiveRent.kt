package com.keyway.core.entities

import java.math.BigDecimal
import java.time.LocalDate
import java.time.OffsetDateTime

data class EffectiveRent(
    val id: String,
    val rentListingId: String,
    val concessionIds: List<String>,
    val dateFrom: LocalDate,
    val dateTo: LocalDate,
    val rent: Money,
    val rentDeposit: Money?,
    val concessions: String,
    val createdAt: OffsetDateTime,
    val updateAt: OffsetDateTime,
    val isActive: Boolean = true,
    val propertyId: String?,
    val type: RentListingType?,
    val typeId: String?,
    val recordSource: String?,
    val zipCode: String?,
    val msaCode: String?,
    val unitSquareFootage: BigDecimal?,
    val bedroomsQuantity: Int?,
    val bathroomsQuantity: BigDecimal?,
    val floorPlan: String?,
) {
    override fun equals(other: Any?): Boolean {
        if (this === other) return true
        if (javaClass != other?.javaClass) return false

        other as EffectiveRent

        if (id != other.id) return false
        if (rentListingId != other.rentListingId) return false
        if (concessionIds != other.concessionIds) return false
        if (dateFrom != other.dateFrom) return false
        if (dateTo != other.dateTo) return false
        if (rent != other.rent) return false
        if (rentDeposit != other.rentDeposit) return false
        if (concessions != other.concessions) return false
        if (isActive != other.isActive) return false

        return true
    }

    override fun hashCode(): Int = super.hashCode()

    override fun toString(): String = "EffectiveRent(id='$id', rentListingId='$rentListingId', date=$dateFrom-$dateTo, rent=${rent.value}, isActive=$isActive)"

    companion object {
        fun build(
            id: String,
            concessionIds: List<String>,
            dateFrom: LocalDate,
            dateTo: LocalDate,
            rent: Money,
            rentDeposit: Money?,
            concessions: String,
            createdAt: OffsetDateTime,
            updateAt: OffsetDateTime,
            isActive: Boolean = true,
            listing: RentListing,
        ) = EffectiveRent(
            id = id,
            rentListingId = listing.id,
            concessionIds = concessionIds,
            dateFrom = dateFrom,
            dateTo = dateTo,
            rent = rent,
            rentDeposit = rentDeposit,
            concessions = concessions,
            createdAt = createdAt,
            updateAt = updateAt,
            isActive = isActive,
            propertyId = listing.propertyId,
            type = listing.type,
            typeId = listing.typeId,
            recordSource = listing.recordSource,
            zipCode = listing.zipCode,
            msaCode = listing.msaCode,
            unitSquareFootage = listing.unitSquareFootage,
            bedroomsQuantity = listing.bedroomsQuantity,
            bathroomsQuantity = listing.bathroomsQuantity,
            floorPlan = listing.floorPlan,
        )
    }
}
