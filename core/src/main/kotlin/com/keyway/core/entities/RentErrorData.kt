package com.keyway.core.entities

import java.math.BigDecimal
import java.time.LocalDate
import java.time.OffsetDateTime

data class RentErrorData(
    val propertyId: String,
    val type: RentListingType,
    val typeId: String,
    val recordId: String?,
    val recordSource: String,
    val zipCode: String,
    val msaCode: String,
    val unitSquareFootage: BigDecimal?,
    val bedrooms: Int,
    val bathrooms: BigDecimal?,
    val floorPlan: String?,
    val availableIn: LocalDate?,
    val rent: Money,
    val rentDeposit: Money?,
    val recordDate: LocalDate,
    val errorType: RentErrorType,
    val createdAt: OffsetDateTime,
    val updateAt: OffsetDateTime,
)
