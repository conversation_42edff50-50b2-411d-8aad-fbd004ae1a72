import org.jetbrains.kotlin.gradle.dsl.JvmTarget
import org.jetbrains.kotlin.gradle.tasks.KotlinCompile

val log4jVersion: String by rootProject
val micrometerVersion: String by rootProject
val jacksonVersion: String by rootProject
val kommonsMapperVersion: String by rootProject

plugins {
    base
    kotlin("jvm") version "2.2.0"
    kotlin("kapt") version "2.2.0"
    idea
    jacoco
    application
    id("org.jlleitschuh.gradle.ktlint") version "12.3.0"
}

buildscript {

    dependencies {
        classpath(kotlin("gradle-plugin", version = "2.2.0"))
        classpath(kotlin("serialization", version = "2.2.0"))
    }
}

allprojects {
    group = "com.keyway"
    version = "1.0.0"

    apply(plugin = "kotlin")
    apply(plugin = "org.jlleitschuh.gradle.ktlint")

    configurations.all {
        resolutionStrategy {
            force("com.pinterest.ktlint:ktlint-cli:1.6.0")
            force("ch.qos.logback:logback-classic:1.5.18")
            force("io.netty:netty-common:4.2.2.Final")
            force("org.apache.commons:commons-lang3:3.18.0")
        }
    }

    repositories {
        mavenCentral()
        listOf("kommons-sqs", "kommons-http", "kommons-auth0", "kommons-mapper", "kommons-db")
            .forEach { projectId ->
                maven {
                    url = uri("https://maven.pkg.github.com/unlockre/$projectId")
                    credentials {
                        username = project.findProperty("gpr.user") as String? ?: System.getenv("GH_USERNAME")
                        password = project.findProperty("gpr.key") as String? ?: System.getenv("GH_TOKEN")
                    }
                }
            }
    }
}

subprojects {

    dependencies {

        // Log
        implementation("org.apache.logging.log4j:log4j-api:$log4jVersion")
        implementation("org.apache.logging.log4j:log4j-core:$log4jVersion")
        implementation("org.apache.logging.log4j:log4j-slf4j2-impl:$log4jVersion")

        // Metrics
        implementation("io.micrometer:micrometer-registry-datadog:$micrometerVersion")

        // Mapper
        implementation("com.fasterxml.jackson.module:jackson-module-kotlin:$jacksonVersion")
        implementation("com.fasterxml.jackson.datatype:jackson-datatype-jsr310:$jacksonVersion")
        implementation("com.fasterxml.jackson.dataformat:jackson-dataformat-yaml:$jacksonVersion")
        implementation("com.fasterxml.jackson.core:jackson-databind:$jacksonVersion")
        implementation("com.keyway:kommons-mapper:$kommonsMapperVersion") {
            exclude(group = "com.google.code.gson")
        }
    }

    tasks.test {
        useJUnitPlatform()
        testLogging {
            lifecycle {
                events =
                    mutableSetOf(
                        org.gradle.api.tasks.testing.logging.TestLogEvent.FAILED,
                        org.gradle.api.tasks.testing.logging.TestLogEvent.PASSED,
                        org.gradle.api.tasks.testing.logging.TestLogEvent.SKIPPED,
                    )
                exceptionFormat = org.gradle.api.tasks.testing.logging.TestExceptionFormat.FULL
                showExceptions = true
                showCauses = true
                showStackTraces = true
                showStandardStreams = true
            }
            info.events = lifecycle.events
            info.exceptionFormat = lifecycle.exceptionFormat
        }

        addTestListener(
            object : TestListener {
                override fun beforeSuite(suite: TestDescriptor) {}

                override fun beforeTest(testDescriptor: TestDescriptor) {}

                override fun afterTest(
                    testDescriptor: TestDescriptor,
                    result: TestResult,
                ) {}

                override fun afterSuite(
                    suite: TestDescriptor,
                    result: TestResult,
                ) {
                    if (suite.parent == null) { // root suite
                        logger.lifecycle("----")
                        logger.lifecycle("Test result: ${result.resultType}")
                        logger.lifecycle(
                            "Test summary: ${result.testCount} tests, " +
                                "${result.successfulTestCount} succeeded, " +
                                "${result.failedTestCount} failed, " +
                                "${result.skippedTestCount} skipped",
                        )
                    }
                }
            },
        )
    }

    tasks.withType<KotlinCompile> {
        compilerOptions {
            jvmTarget.set(JvmTarget.JVM_17)
        }
    }

    tasks.register<Copy>("installGitHook") {
        from("$rootDir/scripts/pre-commit")
        into("$rootDir/.git/hooks/.")
        fileMode = Integer.parseInt("0777", 8)
    }
}
