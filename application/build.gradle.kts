import com.github.jengelman.gradle.plugins.shadow.tasks.ShadowJar
import org.jetbrains.kotlin.gradle.dsl.JvmTarget
import org.jetbrains.kotlin.gradle.tasks.KotlinCompile

val koinVersion: String by rootProject
val jacksonVersion: String by rootProject
val commonsLangVersion: String by rootProject
val kommonsMapperVersion: String by rootProject
val kommonsDbVersion: String by rootProject
val ktorSwaggerUiVersion: String by rootProject
val ktorKeneratorVersion: String by rootProject
val postgresVersion: String by rootProject
val hikariVersion: String by rootProject
val kommonsSqsVersion: String by rootProject
val awsSdkVersion: String by rootProject
val kommonsAwsVersion: String by rootProject
val awsKotlinSdkVersion: String by rootProject

// Application
val mainClassFqn: String = "com.keyway.application.Application"

application {
    mainClass.set(mainClassFqn)
}

plugins {
    kotlin("jvm")
    kotlin("kapt")
    application
    id("com.gradleup.shadow") version "8.3.8"
    kotlin("plugin.serialization")
    id("io.ktor.plugin") version "3.2.1"
    id("org.flywaydb.flyway") version "11.10.2"
}

buildscript {
    dependencies {
        classpath("org.flywaydb:flyway-database-postgresql:11.10.2")
    }
}

dependencies {
    implementation(project(":core"))
    implementation(project(":adapters"))

    implementation("io.ktor:ktor-server-call-logging")
    implementation("io.ktor:ktor-server-content-negotiation")
    implementation("io.ktor:ktor-server-core")
    implementation("io.ktor:ktor-server-netty")
    implementation("io.ktor:ktor-server-status-pages")
    implementation("io.ktor:ktor-server-cors")
    implementation("io.ktor:ktor-serialization-jackson") {
        exclude(group = "com.fasterxml.jackson.core")
        exclude(group = "com.fasterxml.jackson.module", module = "jackson-module-kotlin")
    }
    // Swagger
    implementation("io.github.smiley4:ktor-swagger-ui:$ktorSwaggerUiVersion")
    implementation("io.github.smiley4:ktor-openapi:$ktorSwaggerUiVersion")
    implementation("io.swagger.core.v3:swagger-core:2.2.34")
    implementation("io.github.smiley4:schema-kenerator-core:$ktorKeneratorVersion")
    implementation("io.github.smiley4:schema-kenerator-swagger:$ktorKeneratorVersion")

    // Database
    implementation("com.keyway:kommons-db:$kommonsDbVersion")
    implementation("org.postgresql:postgresql:$postgresVersion")
    implementation("com.zaxxer:HikariCP:$hikariVersion")

    // IoC
    // Koin Core features
    implementation("io.insert-koin:koin-core:$koinVersion")

    // Mapper
    implementation("com.fasterxml.jackson.module:jackson-module-kotlin:$jacksonVersion")
    implementation("com.fasterxml.jackson.datatype:jackson-datatype-jsr310:$jacksonVersion")
    implementation("com.fasterxml.jackson.dataformat:jackson-dataformat-yaml:$jacksonVersion")
    implementation("com.fasterxml.jackson.core:jackson-databind:$jacksonVersion")
    // Mapper
    implementation("com.keyway:kommons-mapper:$kommonsMapperVersion") {
        exclude(group = "com.google.code.gson")
    }

    // AWS
    implementation("com.keyway:kommons-aws:$kommonsAwsVersion")
    implementation(platform("software.amazon.awssdk:bom:$awsSdkVersion"))
    implementation("software.amazon.awssdk:sqs")
    implementation("software.amazon.awssdk:s3")

    implementation("aws.sdk.kotlin:cloudwatch:$awsKotlinSdkVersion")

    // SQS
    implementation("com.keyway:kommons-sqs:$kommonsSqsVersion")

    // Utils
    implementation("org.apache.commons:commons-lang3:$commonsLangVersion")

    implementation("org.jetbrains.kotlin:kotlin-reflect")
}

tasks {

    withType<KotlinCompile> {
        compilerOptions {
            jvmTarget.set(JvmTarget.JVM_17)
            freeCompilerArgs.add("-Xopt-in=kotlin.RequiresOptIn")
        }
    }

    named<ShadowJar>("shadowJar") {
        archiveFileName.set("service.jar")
        manifest {
            attributes(mapOf("Main-Class" to application.mainClass.get()))
        }
    }

    named<JavaExec>("run") {
        doFirst {
            args = listOf("run")
        }
    }
}

// Migration
flyway {
    locations = arrayOf("filesystem:src/main/resources/db/migration")
    user = System.getenv("POSTGRES_DB_USER") ?: "postgres"
    password = System.getenv("POSTGRES_DB_PASSWORD") ?: "postgres"
    url = System.getenv("POSTGRES_DB_URL") ?: "*************************************"
}
