package com.keyway.application.ktor.plugins

import io.ktor.server.application.Application
import io.ktor.server.application.install
import io.ktor.server.plugins.calllogging.CallLogging
import io.ktor.server.request.header
import io.ktor.server.request.path
import org.apache.commons.lang3.RandomStringUtils

object LoggingPlugin {
    private const val TRACE_ID = "trace_id"
    private const val TRACE_SIZE = 16

    private fun generateTraceId(): String = RandomStringUtils.randomAlphanumeric(TRACE_SIZE)

    fun Application.loggingModule() {
        install(CallLogging) {
            mdc(TRACE_ID) { call ->
                call.request.header("X-Correlation-Id") ?: generateTraceId()
            }
            filter { call ->
                call.request.path().startsWith("/health")
            }
        }
    }
}
