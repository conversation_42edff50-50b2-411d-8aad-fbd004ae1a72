package com.keyway.application.ktor.plugins

import com.keyway.application.configuration.model.Configuration
import com.keyway.core.entities.Money
import io.github.smiley4.ktoropenapi.OpenApi
import io.github.smiley4.ktoropenapi.config.SchemaGenerator
import io.github.smiley4.ktoropenapi.config.SchemaOverwriteModule
import io.github.smiley4.schemakenerator.swagger.data.RefType
import io.github.smiley4.schemakenerator.swagger.data.TitleType
import io.ktor.server.application.Application
import io.ktor.server.application.install
import io.swagger.v3.oas.models.media.Schema
import java.math.BigDecimal

object SwaggerPlugin {
    fun Application.swaggerModule(config: Configuration) {
        install(OpenApi) {
            info {
                title = "Rent Api"
                version = "1.0.0"
            }
            server {
                url = config.system.apiUrl
            }
            schemas {
                generator =
                    SchemaGenerator.reflection {
                        explicitNullTypes = false
                        title = TitleType.SIMPLE
                        referencePath = RefType.SIMPLE
                        overwrite(SchemaGenerator.TypeOverwrites.LocalDate())
                        overwrite(
                            SchemaOverwriteModule(
                                identifier = java.time.OffsetDateTime::class.qualifiedName!!,
                                schema = {
                                    Schema<Any>().also {
                                        it.types = setOf("string")
                                        it.format = "date-time"
                                    }
                                },
                            ),
                        )
                        overwrite(
                            SchemaOverwriteModule(
                                identifier = BigDecimal::class.qualifiedName!!,
                                schema = {
                                    Schema<Any>().also {
                                        it.types = setOf("number")
                                        it.format = "double"
                                    }
                                },
                            ),
                        )
                        overwrite(
                            SchemaOverwriteModule(
                                identifier = Money::class.qualifiedName!!,
                                schema = {
                                    Schema<Any>().also {
                                        it.types = setOf("number")
                                        it.format = "double"
                                    }
                                },
                            ),
                        )
                    }
            }
        }
    }
}
