package com.keyway.application.ktor.plugins

import com.keyway.application.router.Router
import io.github.smiley4.ktoropenapi.openApi
import io.github.smiley4.ktorswaggerui.swaggerUI
import io.ktor.http.HttpHeaders.AccessControlAllowOrigin
import io.ktor.http.HttpHeaders.Authorization
import io.ktor.http.HttpHeaders.ContentType
import io.ktor.http.HttpMethod.Companion.Delete
import io.ktor.http.HttpMethod.Companion.Get
import io.ktor.http.HttpMethod.Companion.Options
import io.ktor.http.HttpMethod.Companion.Post
import io.ktor.http.HttpMethod.Companion.Put
import io.ktor.server.application.Application
import io.ktor.server.application.install
import io.ktor.server.plugins.cors.routing.CORS
import io.ktor.server.routing.route
import io.ktor.server.routing.routing

object RoutingPlugin {
    private const val DOCUMENTATION_PATH = "api/docs"
    private const val SWAGGER_PATH = "swagger"

    fun Application.routingModule(routes: Set<Router>) {
        install(CORS) {
            allowHeader(Authorization)
            allowHeader(ContentType)
            allowHeader(AccessControlAllowOrigin)
            allowMethod(Options)
            allowMethod(Get)
            allowMethod(Put)
            allowMethod(Post)
            allowMethod(Delete)
            anyHost()
        }
        routing {
            route(DOCUMENTATION_PATH) { openApi() }
            route(SWAGGER_PATH) { swaggerUI("/$DOCUMENTATION_PATH") }
            routes.forEach { it.setUpRoutes(this) }
        }
    }
}
