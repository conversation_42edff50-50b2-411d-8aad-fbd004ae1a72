package com.keyway.application.ktor.plugins

import com.keyway.application.mapper.AppMapperConfigs
import io.ktor.http.ContentType
import io.ktor.serialization.jackson.JacksonConverter
import io.ktor.server.application.Application
import io.ktor.server.application.install
import io.ktor.server.plugins.contentnegotiation.ContentNegotiation

object SerializationPlugin {
    fun Application.serializationModule() {
        install(ContentNegotiation) {
            register(ContentType.Application.Json, JacksonConverter(AppMapperConfigs.lowerCamelCaseObjectMapper))
        }
    }
}
