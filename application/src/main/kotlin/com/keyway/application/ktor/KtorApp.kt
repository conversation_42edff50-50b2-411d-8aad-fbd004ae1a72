package com.keyway.application.ktor

import com.keyway.application.configuration.model.Configuration
import com.keyway.application.ktor.plugins.ExceptionPlugin.exceptionModule
import com.keyway.application.ktor.plugins.LoggingPlugin.loggingModule
import com.keyway.application.ktor.plugins.RoutingPlugin.routingModule
import com.keyway.application.ktor.plugins.SerializationPlugin.serializationModule
import com.keyway.application.ktor.plugins.SwaggerPlugin.swaggerModule
import com.keyway.application.router.Router
import io.ktor.server.engine.EmbeddedServer
import io.ktor.server.engine.embeddedServer
import io.ktor.server.netty.Netty
import io.ktor.server.netty.NettyApplicationEngine

object KtorApp {
    private lateinit var app: EmbeddedServer<NettyApplicationEngine, NettyApplicationEngine.Configuration>

    fun createApp(
        configuration: Configuration,
        routes: Set<Router>,
    ): EmbeddedServer<NettyApplicationEngine, NettyApplicationEngine.Configuration> {
        if (!this::app.isInitialized) {
            app =
                embeddedServer(Netty, port = configuration.system.httpPort) {
                    loggingModule()
                    swaggerModule(config = configuration)
                    serializationModule()
                    exceptionModule()
                    routingModule(routes)
                }
        }
        return app
    }
}
