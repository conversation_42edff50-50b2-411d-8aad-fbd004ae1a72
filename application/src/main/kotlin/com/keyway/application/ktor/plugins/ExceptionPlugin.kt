package com.keyway.application.ktor.plugins

import com.keyway.adapters.exceptions.BadRequestException
import com.keyway.adapters.exceptions.ExceptionLogLevel
import com.keyway.adapters.exceptions.InternalServerException
import com.keyway.adapters.exceptions.RestException
import io.ktor.server.application.Application
import io.ktor.server.application.install
import io.ktor.server.plugins.MissingRequestParameterException
import io.ktor.server.plugins.statuspages.StatusPages
import io.ktor.server.response.respond
import org.slf4j.LoggerFactory

object ExceptionPlugin {
    private val logger = LoggerFactory.getLogger(this.javaClass)

    fun Application.exceptionModule() {
        install(StatusPages) {
            exception<Throwable> { call, throwable ->
                when (throwable) {
                    is RestException ->
                        throwable
                            .getResponse()
                            .let { response ->
                                handleLog(throwable, "[EXCEPTION]. $response")
                                call.respond(
                                    io.ktor.http.HttpStatusCode
                                        .fromValue(throwable.httpStatusCode),
                                    response,
                                )
                            }

                    is MissingRequestParameterException ->
                        BadRequestException(message = "Parameter ${throwable.parameterName} is required or has invalid format")
                            .also { logger.warn("[BAD_REQUEST] ${it.getResponse()}", throwable) }
                            .let {
                                call.respond(
                                    io.ktor.http.HttpStatusCode.BadRequest,
                                    it.getResponse(),
                                )
                            }

                    is Exception ->
                        InternalServerException(cause = throwable)
                            .also { logger.error("[UNHANDLED_EXCEPTION]. ${it.getResponse()}", throwable) }
                            .let {
                                call.respond(
                                    io.ktor.http.HttpStatusCode
                                        .fromValue(it.httpStatusCode),
                                    it.getResponse(),
                                )
                            }
                }
            }
        }
    }

    private fun handleLog(
        throwable: RestException,
        message: String,
    ) = when (throwable.getLoggerLevel()) {
        ExceptionLogLevel.ERROR -> logger.error(message)
        ExceptionLogLevel.WARN -> logger.warn(message)
        ExceptionLogLevel.INFO -> logger.info(message)
    }
}
