package com.keyway.application.mapper

import com.fasterxml.jackson.databind.PropertyNamingStrategies
import com.keyway.kommons.mapper.JsonMapper
import com.keyway.kommons.mapper.jackson.JacksonMapper
import com.keyway.kommons.mapper.jackson.useDefaultJsonConfig

object AppMapperConfigs {
    val lowerCamelCaseObjectMapper =
        useDefaultJsonConfig()
            .apply {
                this.propertyNamingStrategy = PropertyNamingStrategies.LowerCamelCaseStrategy()
            }.also { JsonMapper.setMapper(JacksonMapper(it)) }
}
