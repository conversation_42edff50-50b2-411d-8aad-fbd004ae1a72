package com.keyway.application.koin.modules

import com.keyway.adapters.repositories.MultifamilyPropertyPostgresRepository
import com.keyway.adapters.repositories.PostgresEffectiveRentRepository
import com.keyway.adapters.repositories.PostgresHistoricDataRepository
import com.keyway.adapters.repositories.PostgresHistoricalRentRepository
import com.keyway.adapters.repositories.PostgresListingsRepository
import com.keyway.adapters.repositories.PostgresMetricsRepository
import com.keyway.adapters.repositories.PostgresPropertyConcessionV2Repository
import com.keyway.adapters.repositories.PostgresPropertyMetricRepository
import com.keyway.adapters.repositories.PostgresPropertyUnitRepository
import com.keyway.adapters.repositories.PostgresSummarizedMetricsRepository
import com.keyway.adapters.repositories.PostgresTransactionRepository
import com.keyway.adapters.repositories.PostgresUnitSimilarityCalculatorConfigRepository
import com.keyway.core.ports.repositories.EffectiveRentRepository
import com.keyway.core.ports.repositories.HistoricDataRepository
import com.keyway.core.ports.repositories.HistoricalRentRepository
import com.keyway.core.ports.repositories.ListingsRepository
import com.keyway.core.ports.repositories.MetricsRepository
import com.keyway.core.ports.repositories.MultifamilyPropertyRepository
import com.keyway.core.ports.repositories.PropertyConcessionV2Repository
import com.keyway.core.ports.repositories.PropertyMetricsRepository
import com.keyway.core.ports.repositories.PropertyUnitRepository
import com.keyway.core.ports.repositories.SummarizedMetricsRepository
import com.keyway.core.ports.repositories.TransactionRepository
import com.keyway.core.ports.repositories.UnitSimilarityCalculatorConfigRepository
import org.koin.core.module.Module
import org.koin.dsl.module

object RepositoriesModule : KoinModule {
    override fun get(): Module =
        module(createdAtStart = true) {
            single<TransactionRepository> { PostgresTransactionRepository(get()) }
            single<EffectiveRentRepository> { PostgresEffectiveRentRepository(get()) }
            single<ListingsRepository> { PostgresListingsRepository(get()) }
            single<MetricsRepository> { PostgresMetricsRepository(get()) }
            single<PropertyConcessionV2Repository> { PostgresPropertyConcessionV2Repository(get()) }

            single<HistoricalRentRepository> { PostgresHistoricalRentRepository(get()) }
            single<HistoricDataRepository> { PostgresHistoricDataRepository(get()) }
            single<PropertyUnitRepository> { PostgresPropertyUnitRepository(get()) }
            single<MultifamilyPropertyRepository> { MultifamilyPropertyPostgresRepository(get(), get()) }
            single<UnitSimilarityCalculatorConfigRepository> { PostgresUnitSimilarityCalculatorConfigRepository(get()) }
            single<PropertyMetricsRepository> { PostgresPropertyMetricRepository(get()) }
            single<SummarizedMetricsRepository> { PostgresSummarizedMetricsRepository(get()) }
        }
}
