package com.keyway.application.koin.modules

import com.keyway.adapters.executor.BaseUseCaseExecutor
import com.keyway.adapters.executor.UseCaseExecutor
import com.keyway.core.service.concession.SaveConcessionServiceV2
import com.keyway.core.service.listing.GetPropertiesListingService
import com.keyway.core.service.listing.GroupedListingService
import com.keyway.core.usecases.backgorund.tasks.DeleteInactiveListingDataUseCase
import com.keyway.core.usecases.backgorund.tasks.NotifyLastRentByPropertyUseCase
import com.keyway.core.usecases.concessions.GetConcessionTypesDistributionUseCase
import com.keyway.core.usecases.concessions.GetPropertiesConcessionsV2UseCase
import com.keyway.core.usecases.concessions.SaveConcessionsV2UseCase
import com.keyway.core.usecases.historical.GetHistoricalRents
import com.keyway.core.usecases.listings.CalculateRentSuggestionUseCase
import com.keyway.core.usecases.listings.GetFutureAvailabilityUseCase
import com.keyway.core.usecases.listings.GetPropertiesLastListingUseCase
import com.keyway.core.usecases.listings.GetPropertiesListingsUseCase
import com.keyway.core.usecases.listings.GetPropertyLastListingUseCase
import com.keyway.core.usecases.listings.GetPropertyListingsUseCase
import com.keyway.core.usecases.listings.ListingsWithEffectiveRentMapper
import com.keyway.core.usecases.listings.SaveGroupedListingUseCase
import com.keyway.core.usecases.listings.SaveHistoricalGroupedListingUseCase
import com.keyway.core.usecases.metrics.ComputeAggregatedGeoMetricUseCase
import com.keyway.core.usecases.metrics.ComputeAggregatedMetricUseCase
import com.keyway.core.usecases.multifamily.SaveOrUpdateProperty
import com.keyway.core.usecases.multifamily.units.SaveOrUpdatePropertyUnits
import org.koin.core.module.Module
import org.koin.dsl.module

object UseCasesModule : KoinModule {
    override fun get(): Module =
        module(createdAtStart = true) {
            single<UseCaseExecutor> { BaseUseCaseExecutor }

            single { GroupedListingService(get()) }

            single { SaveGroupedListingUseCase(get(), get(), get(), get()) }
            single { SaveHistoricalGroupedListingUseCase(get(), get(), get()) }

            single { ListingsWithEffectiveRentMapper(get()) }
            single { GetPropertyLastListingUseCase(get(), get()) }
            single { GetPropertyListingsUseCase(get(), get()) }
            single { GetPropertiesListingsUseCase(get(), get()) }
            single { GetPropertiesLastListingUseCase(get(), get()) }
            single { GetFutureAvailabilityUseCase(get()) }

            single { ComputeAggregatedMetricUseCase(get()) }
            single { ComputeAggregatedGeoMetricUseCase(get()) }

            single { SaveConcessionsV2UseCase(get(), get(), get(), get()) }
            single { SaveConcessionServiceV2() }
            single { GetConcessionTypesDistributionUseCase(get()) }

            single { GetHistoricalRents(get()) }
            single { GetPropertiesConcessionsV2UseCase(get()) }

            single { DeleteInactiveListingDataUseCase(get(), get(), get()) }

            single { SaveOrUpdatePropertyUnits(get()) }
            single { SaveOrUpdateProperty() }

            single { CalculateRentSuggestionUseCase(get(), get(), get(), get()) }

            single { NotifyLastRentByPropertyUseCase(get(), get()) }

            single { GetPropertiesListingService(get()) }
        }
}
