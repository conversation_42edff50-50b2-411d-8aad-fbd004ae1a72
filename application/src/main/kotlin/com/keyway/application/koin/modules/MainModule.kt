package com.keyway.application.koin.modules

import com.keyway.application.configuration.model.Configuration
import com.keyway.application.configuration.parser.ConfigParser
import com.keyway.application.koin.ModuleConstants
import com.keyway.application.koin.ModuleConstants.ROUTES
import com.keyway.application.ktor.KtorApp
import com.keyway.application.mapper.AppMapperConfigs.lowerCamelCaseObjectMapper
import com.keyway.core.utils.DateUtils
import com.keyway.core.utils.IdGenerator
import com.keyway.kommons.mapper.Mapper
import com.keyway.kommons.mapper.jackson.JacksonMapper
import org.koin.core.module.Module
import org.koin.core.qualifier.named
import org.koin.dsl.module
import java.time.Clock
import java.util.UUID

object MainModule : KoinModule {
    override fun get(): Module =
        module(createdAtStart = true) {
            single { KtorApp.createApp(get(), get(named(ROUTES))) }

            // Configuration
            single { ConfigParser.read() }
            single { get<Configuration>().system }
            single { get<Configuration>().datadogConfig }
            single { get<Configuration>().dataBaseConfig }
            single { get<Configuration>().awsConfig }
            single { get<Configuration>().sqsConfig }

            // Mappers
            single<Mapper>(named(ModuleConstants.APP_MAPPER)) { JacksonMapper(lowerCamelCaseObjectMapper) }

            single<IdGenerator> { IdGenerator { UUID.randomUUID().toString() } }

            // Clock
            single<Clock> { Clock.systemUTC() }
            single { DateUtils.initialize(get()) }
        }
}
