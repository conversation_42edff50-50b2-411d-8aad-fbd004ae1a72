package com.keyway.application.koin.modules

import com.keyway.kommons.db.DataSourceMaker
import com.keyway.kommons.db.SqlClient
import org.koin.core.module.Module
import org.koin.dsl.module

object DataBaseModule : KoinModule {
    override fun get(): Module =
        module(createdAtStart = true) {
            single { DataSourceMaker.createDataSource(get()) }
            single { SqlClient(get()) }
        }
}
