package com.keyway.application.koin.modules.loader

import com.keyway.application.koin.modules.DataBaseModule
import com.keyway.application.koin.modules.MainModule
import com.keyway.application.koin.modules.RepositoriesModule
import com.keyway.application.koin.modules.RoutingModule
import com.keyway.application.koin.modules.SqsModule
import com.keyway.application.koin.modules.UseCasesModule

object ModuleLoader {
    val modules =
        mutableListOf(
            RoutingModule.get(),
            MainModule.get(),
            RepositoriesModule.get(),
            UseCasesModule.get(),
            SqsModule.get(),
            DataBaseModule.get(),
        )
}
