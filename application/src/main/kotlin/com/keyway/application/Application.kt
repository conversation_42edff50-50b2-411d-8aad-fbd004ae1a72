package com.keyway.application

import com.keyway.application.configuration.model.Configuration
import com.keyway.application.configuration.model.CustomDatadogConfig
import com.keyway.application.koin.ModuleConstants
import com.keyway.application.koin.starter.KoinStarter
import com.keyway.kommons.sqs.SqsConsumer
import io.ktor.server.engine.EmbeddedServer
import io.ktor.server.netty.NettyApplicationEngine
import io.micrometer.core.instrument.Clock
import io.micrometer.core.instrument.Metrics
import io.micrometer.datadog.DatadogMeterRegistry
import org.apache.commons.lang3.RandomStringUtils
import org.koin.core.component.KoinComponent
import org.koin.core.component.inject
import org.koin.core.qualifier.named
import org.slf4j.LoggerFactory
import org.slf4j.MDC

class Application : KoinComponent {
    private val logger = LoggerFactory.getLogger(this.javaClass)
    private val app: EmbeddedServer<NettyApplicationEngine, NettyApplicationEngine.Configuration> by inject()
    private val configuration: Configuration by inject()
    private val customDatadogConfig: CustomDatadogConfig by inject()
    private val consumers: Set<SqsConsumer> by inject(named(ModuleConstants.CONSUMERS))

    companion object {
        const val TRACE_ID = "trace_id"
        private const val TRACE_SIZE = 16

        fun generateTraceId(): String = RandomStringUtils.randomAlphanumeric(TRACE_SIZE)

        @JvmStatic
        fun main(args: Array<String>) {
            KoinStarter.start()
            Application().init()
        }
    }

    fun init() {
        MDC.put(TRACE_ID, generateTraceId())
        runCatching {
            logger.info("APP_INIT: Waiting for initialization...")

            Metrics.addRegistry(
                DatadogMeterRegistry(customDatadogConfig, Clock.SYSTEM),
            )

            if (configuration.enableSqsConsumers) {
                logger.info("Starting SQS Consumers")
                consumers.forEach {
                    it.start()
                }
            }

            logger.info("Application already initialized and listen at port: ${configuration.system.httpPort}")
            app.start(true)
        }.onFailure { e ->
            logger.error("BOOT_ERROR: " + e.message, e)
            throw e
        }.getOrDefault(Unit)
            .run { MDC.clear() }
    }
}
