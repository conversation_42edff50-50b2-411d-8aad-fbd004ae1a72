package com.keyway.application.router.rent

import com.keyway.adapters.dtos.listings.PropertiesListingDataInput
import com.keyway.adapters.dtos.listings.RentListingsResponse
import com.keyway.adapters.handlers.rest.units.GetPropertiesLastListingHandler
import com.keyway.application.router.Router
import com.keyway.application.utils.router.ParamUtils.DATE_FROM_PARAM_NAME
import com.keyway.application.utils.router.ParamUtils.DATE_TO_PARAM_NAME
import com.keyway.application.utils.router.ParamUtils.propIdsBaseParameterDoc
import com.keyway.application.utils.router.RequestUtils.getLocalDateQueryParameter
import com.keyway.application.utils.router.RequestUtils.getPropertyIds
import io.github.smiley4.ktoropenapi.get
import io.github.smiley4.ktoropenapi.route
import io.ktor.http.HttpStatusCode
import io.ktor.server.response.respond
import io.ktor.server.routing.Routing

class GetPropertiesLastListingRouter(
    private val getPropertiesLastListingHandler: GetPropertiesLastListingHandler,
) : Router {
    companion object {
        const val BASE_URL = "/multifamily/units"
        const val TAG = "propertyUnitRent"
    }

    override fun setUpRoutes(routing: Routing) {
        routing.route {
            get(
                BASE_URL,
                {
                    tags = listOf(TAG)
                    summary = "Properties last unit rent data"
                    description =
                        """
                        Allows obtaining detailed information about the last registered data about units rent for a specific set of properties. This information includes rent, deposit, and the listed time period. 
                        It ensures returning a single listing per unit and the most recent one available in the database.
                        """.trimIndent()
                    response {
                        HttpStatusCode.OK to { body<List<RentListingsResponse>>() }
                    }
                    propIdsBaseParameterDoc()
                },
            ) {
                val httpInput =
                    PropertiesListingDataInput(
                        propertiesIds = call.getPropertyIds(),
                        dateFrom = call.getLocalDateQueryParameter(DATE_FROM_PARAM_NAME),
                        dateTo = call.getLocalDateQueryParameter(DATE_TO_PARAM_NAME),
                        bedrooms = null,
                        bathrooms = null,
                    )
                call.respond(getPropertiesLastListingHandler.invoke(httpInput))
            }
        }
    }
}
