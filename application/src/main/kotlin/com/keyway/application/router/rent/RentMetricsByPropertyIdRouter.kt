package com.keyway.application.router.rent

import com.keyway.adapters.dtos.metrics.property.BedroomRentSummary
import com.keyway.adapters.dtos.metrics.property.FloorPlanRentSummary
import com.keyway.adapters.dtos.metrics.property.PropertyRentSummary
import com.keyway.adapters.dtos.metrics.property.UnitMixRentSummary
import com.keyway.adapters.dtos.metrics.property.UnitsRentSummary
import com.keyway.adapters.handlers.rest.metrics.GetPropertyMetricsHandler
import com.keyway.application.router.Router
import com.keyway.application.utils.router.ParamUtils.DATE_FROM_PARAM_NAME
import com.keyway.application.utils.router.ParamUtils.DATE_TO_PARAM_NAME
import com.keyway.application.utils.router.ParamUtils.PROPERTY_ID_PARAM_NAME
import com.keyway.application.utils.router.ParamUtils.UNIT_CONDITION_PARAM_NAME
import com.keyway.application.utils.router.ParamUtils.propIdBaseParameterDoc
import com.keyway.application.utils.router.RequestUtils.getLocalDateQueryParameter
import com.keyway.application.utils.router.RequestUtils.getPropertyId
import com.keyway.application.utils.router.RequestUtils.getStringQueryParameter
import com.keyway.core.entities.UnitCondition
import com.keyway.core.entities.metric.MetricType
import io.github.smiley4.ktoropenapi.get
import io.github.smiley4.ktoropenapi.route
import io.ktor.http.HttpStatusCode
import io.ktor.server.response.respond
import io.ktor.server.routing.Routing
import io.ktor.server.routing.RoutingContext

class RentMetricsByPropertyIdRouter(
    private val getPropertyMetricsHandler: GetPropertyMetricsHandler,
) : Router {
    companion object {
        const val PATH = "/multifamily/{$PROPERTY_ID_PARAM_NAME}/metrics"
        const val TAG = "metrics"
    }

    override fun setUpRoutes(routing: Routing) {
        routing.route {
            get(
                "$PATH/bedrooms",
                {
                    tags = listOf(TAG)
                    summary = "Rent metrics grouped by Bedrooms"
                    propIdBaseParameterDoc()
                    response {
                        HttpStatusCode.OK to { body<BedroomRentSummary>() }
                    }
                },
            ) {
                getMetrics(MetricType.BEDROOMS)
            }

            get(
                "$PATH/property",
                {
                    tags = listOf(TAG)
                    summary = "Rent metrics grouped by Property"
                    propIdBaseParameterDoc()
                    response {
                        HttpStatusCode.OK to { body<PropertyRentSummary>() }
                    }
                },
            ) {
                getMetrics(MetricType.BY_ID)
            }

            get(
                "$PATH/unit-mix",
                {
                    tags = listOf(TAG)
                    summary = "Rent metrics grouped by Unit Mix"
                    propIdBaseParameterDoc()
                    response {
                        HttpStatusCode.OK to { body<UnitMixRentSummary>() }
                    }
                },
            ) {
                getMetrics(MetricType.UNIT_MIX)
            }

            get(
                "$PATH/floor-plan",
                {
                    tags = listOf(TAG)
                    summary = "Rent metrics grouped by Floorplan"
                    propIdBaseParameterDoc()
                    response {
                        HttpStatusCode.OK to { body<FloorPlanRentSummary>() }
                    }
                },
            ) {
                getMetrics(MetricType.FLOOR_PLAN)
            }

            get(
                "$PATH/units",
                {
                    tags = listOf(TAG)
                    summary = "Rent metrics grouped by units"
                    propIdBaseParameterDoc()
                    response {
                        HttpStatusCode.OK to { body<UnitsRentSummary>() }
                    }
                },
            ) {
                getMetrics(MetricType.UNITS)
            }
        }
    }

    private suspend fun RoutingContext.getMetrics(metricType: MetricType) {
        call.respond(
            getPropertyMetricsHandler(
                GetPropertyMetricsHandler.Input(
                    propertyIds = setOf(call.getPropertyId()),
                    dateFrom = call.getLocalDateQueryParameter(DATE_FROM_PARAM_NAME),
                    dateTo = call.getLocalDateQueryParameter(DATE_TO_PARAM_NAME),
                    metricType = metricType,
                    unitCondition = call.getStringQueryParameter(UNIT_CONDITION_PARAM_NAME)?.let { UnitCondition.valueOf(it.uppercase()) },
                ),
            ).first(),
        )
    }
}
