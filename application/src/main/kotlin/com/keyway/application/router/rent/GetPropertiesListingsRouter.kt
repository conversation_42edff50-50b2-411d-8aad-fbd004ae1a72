package com.keyway.application.router.rent

import com.keyway.adapters.dtos.listings.PropertiesListingDataInput
import com.keyway.adapters.dtos.listings.PropertiesListingsResponse
import com.keyway.adapters.handlers.rest.listings.GetPropertiesListingsHandler
import com.keyway.application.router.Router
import com.keyway.application.utils.router.ParamUtils.BATHROOMS_PARAM_NAME
import com.keyway.application.utils.router.ParamUtils.BEDROOMS_PARAM_NAME
import com.keyway.application.utils.router.ParamUtils.DATE_FROM_PARAM_NAME
import com.keyway.application.utils.router.ParamUtils.DATE_TO_PARAM_NAME
import com.keyway.application.utils.router.ParamUtils.propIdsBaseParameterDoc
import com.keyway.application.utils.router.RequestUtils.getBigDecimalQueryParameter
import com.keyway.application.utils.router.RequestUtils.getIntQueryParameter
import com.keyway.application.utils.router.RequestUtils.getLocalDateQueryParameter
import com.keyway.application.utils.router.RequestUtils.getPropertyIds
import io.github.smiley4.ktoropenapi.get
import io.github.smiley4.ktoropenapi.route
import io.ktor.http.HttpStatusCode
import io.ktor.server.response.respond
import io.ktor.server.routing.Routing
import java.math.BigDecimal

class GetPropertiesListingsRouter(
    private val getPropertiesListingsHandler: GetPropertiesListingsHandler,
) : Router {
    companion object {
        const val BASE_URL = "/multifamily/listings"
        const val TAG = "propertyListings"
    }

    override fun setUpRoutes(routing: Routing) {
        routing.route {
            get(
                BASE_URL,
                {
                    tags = listOf(TAG)
                    summary = "Bulk property listings"
                    description =
                        """
                        Allows obtaining detailed information about the listings of property set including the units, rent, deposit, and the time period for which these values apply.
                        It can accept the following parameters:
                        - propertyIds: set of ids to search
                        - dateFrom: start of the period
                        - dateTo: end of the period
                        - bedrooms: number of bedrooms
                        - bathrooms: number of bathrooms
                        If dates are not provided we will return listings since 30 days ago including the futures listings.
                        
                        """.trimIndent()
                    response {
                        HttpStatusCode.OK to { body<List<PropertiesListingsResponse>>() }
                    }
                    propIdsBaseParameterDoc {
                        queryParameter<Int>(BEDROOMS_PARAM_NAME) {
                            description = "Number of bedrooms"
                        }
                        queryParameter<BigDecimal>(BATHROOMS_PARAM_NAME) {
                            description = "Number of bathrooms"
                        }
                    }
                },
            ) {
                val httpInput =
                    PropertiesListingDataInput(
                        propertiesIds = call.getPropertyIds(),
                        dateFrom = call.getLocalDateQueryParameter(DATE_FROM_PARAM_NAME),
                        dateTo = call.getLocalDateQueryParameter(DATE_TO_PARAM_NAME),
                        bedrooms = call.getIntQueryParameter(BEDROOMS_PARAM_NAME),
                        bathrooms = call.getBigDecimalQueryParameter(BATHROOMS_PARAM_NAME),
                    )
                call.respond(getPropertiesListingsHandler.invoke(httpInput))
            }
        }
    }
}
