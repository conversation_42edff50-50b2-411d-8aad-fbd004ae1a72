package com.keyway.application.router.concession

import com.keyway.adapters.dtos.concessions.Concession
import com.keyway.adapters.dtos.concessions.ConcessionResponse
import com.keyway.adapters.dtos.concessions.PropertiesConcessionInput
import com.keyway.adapters.handlers.rest.concessions.GetPropertiesConcessionsHandler
import com.keyway.adapters.handlers.rest.concessions.GetPropertiesConcessionsV2Handler
import com.keyway.application.router.Router
import com.keyway.application.utils.router.ParamUtils.ACTIVE_DESC
import com.keyway.application.utils.router.ParamUtils.ACTIVE_PARAM_NAME
import com.keyway.application.utils.router.ParamUtils.BENEFIT_DESC
import com.keyway.application.utils.router.ParamUtils.BENEFIT_PARAM_NAME
import com.keyway.application.utils.router.ParamUtils.BENEFIT_TYPE_DESC
import com.keyway.application.utils.router.ParamUtils.BENEFIT_TYPE_PARAM_NAME
import com.keyway.application.utils.router.ParamUtils.DATE_FROM_DESC
import com.keyway.application.utils.router.ParamUtils.DATE_FROM_PARAM_NAME
import com.keyway.application.utils.router.ParamUtils.DATE_TO_DESC
import com.keyway.application.utils.router.ParamUtils.DATE_TO_PARAM_NAME
import com.keyway.application.utils.router.ParamUtils.PROP_IDS_DESC
import com.keyway.application.utils.router.ParamUtils.PROP_IDS_PARAM_NAME
import com.keyway.application.utils.router.RequestUtils.getBooleanQueryParameter
import com.keyway.application.utils.router.RequestUtils.getLocalDateQueryParameter
import com.keyway.application.utils.router.RequestUtils.getPropertyIds
import com.keyway.application.utils.router.RequestUtils.getStringQueryParameter
import com.keyway.core.dto.concessions.input.GetPropertiesConcessionsV2Input
import io.github.smiley4.ktoropenapi.config.RequestConfig
import io.github.smiley4.ktoropenapi.config.RouteConfig
import io.github.smiley4.ktoropenapi.get
import io.github.smiley4.ktoropenapi.route
import io.ktor.http.HttpStatusCode
import io.ktor.server.response.respond
import io.ktor.server.routing.Routing
import java.time.LocalDate

class
GetPropertiesConcessionsRouter(
    private val getPropertiesConcessionsHandler: GetPropertiesConcessionsHandler,
    private val getPropertiesConcessionsV2Handler: GetPropertiesConcessionsV2Handler,
) : Router {
    companion object {
        const val BASE_URL = "/multifamily/concessions"
        const val TAG = "propertyConcessions"
    }

    override fun setUpRoutes(routing: Routing) {
        routing.route {
            get(
                "/v1/$BASE_URL",
                {
                    tags = listOf(TAG)
                    summary = "Properties concessions - V1"
                    description =
                        """
                        Allows to obtain the concessions for a set of properties based on their ids.
                        Also, this endpoint has the ability to filter by dateFrom and dateTo, if this parameters are not provided
                        the endpoint would return the results of 30 days ago the query date. In addition you can filter for specific benefits
                        or benefit type.
                        """.trimIndent()
                    propIdsConcessionBaseParameterDoc {
                        queryParameter<String>(BENEFIT_PARAM_NAME) {
                            required = false
                            description = BENEFIT_DESC
                        }
                        queryParameter<String>(BENEFIT_TYPE_PARAM_NAME) {
                            required = false
                            description = BENEFIT_TYPE_DESC
                        }
                    }
                    response {
                        HttpStatusCode.OK to { body<List<ConcessionResponse>>() }
                    }
                },
            ) {
                val httpInput =
                    PropertiesConcessionInput(
                        propertiesIds = call.getPropertyIds(),
                        dateFrom = call.getLocalDateQueryParameter(DATE_FROM_PARAM_NAME),
                        dateTo = call.getLocalDateQueryParameter(DATE_TO_PARAM_NAME),
                        active = call.getBooleanQueryParameter(ACTIVE_PARAM_NAME),
                        benefit = call.getStringQueryParameter(BENEFIT_PARAM_NAME),
                        type = call.getStringQueryParameter(BENEFIT_TYPE_PARAM_NAME),
                    )
                call.respond(getPropertiesConcessionsHandler.invoke(httpInput).sortedByDescending { it.dateTo })
            }

            get(
                "/v2/$BASE_URL",
                {
                    tags = listOf(TAG)
                    summary = "Properties concessions - V2"
                    description =
                        """
                        Allows to obtain the concessions for a set of properties based on their ids.
                        Also, this endpoint has the ability to filter by dateFrom and dateTo, if this parameters are not provided
                        the endpoint would return the results of 30 days ago the query date.
                        """.trimIndent()
                    propIdsConcessionBaseParameterDoc {}
                    response {
                        HttpStatusCode.OK to { body<List<Concession>>() }
                    }
                },
            ) {
                val httpInput =
                    GetPropertiesConcessionsV2Input(
                        propertyIds = call.getPropertyIds(),
                        dateFrom = call.getLocalDateQueryParameter(DATE_FROM_PARAM_NAME),
                        dateTo = call.getLocalDateQueryParameter(DATE_TO_PARAM_NAME),
                        isActive = call.getBooleanQueryParameter(ACTIVE_PARAM_NAME),
                    )
                call.respond(getPropertiesConcessionsV2Handler.invoke(httpInput).sortedByDescending { it.dateTo })
            }
        }
    }

    fun RouteConfig.propIdsConcessionBaseParameterDoc(block: RequestConfig.() -> Unit = {}) =
        request {
            queryParameter<List<String>>(PROP_IDS_PARAM_NAME) {
                required = true
                description = PROP_IDS_DESC
            }
            queryParameter<LocalDate>(DATE_FROM_PARAM_NAME) {
                required = false
                description = DATE_FROM_DESC
            }
            queryParameter<LocalDate>(DATE_TO_PARAM_NAME) {
                required = false
                description = DATE_TO_DESC
            }
            queryParameter<Boolean>(ACTIVE_PARAM_NAME) {
                required = false
                description = ACTIVE_DESC
            }
            block()
        }
}
