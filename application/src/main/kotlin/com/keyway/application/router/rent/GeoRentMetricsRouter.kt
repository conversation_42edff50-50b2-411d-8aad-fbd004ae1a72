package com.keyway.application.router.rent

import com.keyway.adapters.dtos.metrics.geo.MSABedroomRentSummary
import com.keyway.adapters.dtos.metrics.geo.MSARentSummary
import com.keyway.adapters.dtos.metrics.geo.ZipBedroomRentSummary
import com.keyway.adapters.dtos.metrics.geo.ZipRentSummary
import com.keyway.adapters.handlers.rest.listings.GetFutureAvailabilityHandler
import com.keyway.adapters.handlers.rest.metrics.GetGeoMetricsHandler
import com.keyway.application.router.Router
import com.keyway.application.utils.router.ParamUtils.DATE_FROM_DESC
import com.keyway.application.utils.router.ParamUtils.DATE_FROM_PARAM_NAME
import com.keyway.application.utils.router.ParamUtils.DATE_TO_DESC
import com.keyway.application.utils.router.ParamUtils.DATE_TO_PARAM_NAME
import com.keyway.application.utils.router.ParamUtils.MSA_CODE_PARAM
import com.keyway.application.utils.router.ParamUtils.MSA_TAG
import com.keyway.application.utils.router.ParamUtils.ZIP_CODE_PARAM
import com.keyway.application.utils.router.ParamUtils.ZIP_TAG
import com.keyway.application.utils.router.RequestUtils.getLocalDateQueryParameter
import com.keyway.application.utils.router.RequestUtils.getStringPathParameterOrFail
import com.keyway.core.dto.listings.input.AggregatedMetricType
import com.keyway.core.dto.query.metrics.IdType
import io.github.smiley4.ktoropenapi.config.RequestConfig
import io.github.smiley4.ktoropenapi.config.RouteConfig
import io.github.smiley4.ktoropenapi.get
import io.github.smiley4.ktoropenapi.route
import io.ktor.http.HttpStatusCode
import io.ktor.server.response.respond
import io.ktor.server.routing.Routing
import io.ktor.server.routing.RoutingContext
import java.time.LocalDate

class GeoRentMetricsRouter(
    private val getGeoMetricsHandler: GetGeoMetricsHandler,
    private val getFutureAvailabilityHandler: GetFutureAvailabilityHandler,
) : Router {
    override fun setUpRoutes(routing: Routing) {
        routing.route {
            get(
                "/zip-codes/{$ZIP_CODE_PARAM}/bedrooms-metrics-summary",
                {
                    tags = listOf(ZIP_TAG)
                    summary = "Zip Code Rent metrics grouped by Bedrooms"
                    geoMetricsBaseParameterDoc {
                        pathParameter<String>(ZIP_CODE_PARAM) {
                            required = true
                        }
                    }
                    response {
                        HttpStatusCode.OK to { body<ZipBedroomRentSummary>() }
                    }
                },
            ) {
                getMetrics(setOf(call.getStringPathParameterOrFail(ZIP_CODE_PARAM)), IdType.ZIP_CODE, AggregatedMetricType.BEDROOMS)
            }

            get(
                "/zip-codes/{$ZIP_CODE_PARAM}/metrics-summary",
                {
                    tags = listOf(ZIP_TAG)
                    summary = "Zip Code Rent metrics"
                    geoMetricsBaseParameterDoc {
                        pathParameter<String>(ZIP_CODE_PARAM) {
                            required = true
                        }
                    }
                    response {
                        HttpStatusCode.OK to { body<ZipRentSummary>() }
                    }
                },
            ) {
                getMetrics(setOf(call.getStringPathParameterOrFail(ZIP_CODE_PARAM)), IdType.ZIP_CODE, AggregatedMetricType.BY_ID)
            }

            get(
                "/zip-codes/{$ZIP_CODE_PARAM}/future-availability",
                {
                    tags = listOf(ZIP_TAG)
                    summary = "Zip Code future availability"
                    request {
                        pathParameter<String>(ZIP_CODE_PARAM) {
                            required = true
                        }
                    }
                    response {
                        HttpStatusCode.OK to { body<Map<LocalDate, Int>>() }
                    }
                },
            ) {
                call.respond(
                    getFutureAvailabilityHandler(
                        id = call.getStringPathParameterOrFail(ZIP_CODE_PARAM),
                        idType = IdType.ZIP_CODE,
                    ),
                )
            }

            get(
                "/msa-codes/{$MSA_CODE_PARAM}/future-availability",
                {
                    tags = listOf(MSA_TAG)
                    summary = "Msa Code future availability"
                    request {
                        pathParameter<String>(MSA_CODE_PARAM) {
                            required = true
                        }
                    }
                    response {
                        HttpStatusCode.OK to { body<Map<LocalDate, Int>>() }
                    }
                },
            ) {
                call.respond(
                    getFutureAvailabilityHandler(
                        id = call.getStringPathParameterOrFail(MSA_CODE_PARAM),
                        idType = IdType.MSA,
                    ),
                )
            }

            get(
                "/msa-codes/{$MSA_CODE_PARAM}/bedrooms-metrics-summary",
                {
                    tags = listOf(MSA_TAG)
                    summary = "MSA Rent metrics grouped by Bedrooms"
                    geoMetricsBaseParameterDoc {
                        pathParameter<String>(MSA_CODE_PARAM) {
                            required = true
                        }
                    }
                    response {
                        HttpStatusCode.OK to { body<MSABedroomRentSummary>() }
                    }
                },
            ) {
                getMetrics(setOf(call.getStringPathParameterOrFail(MSA_CODE_PARAM)), IdType.MSA, AggregatedMetricType.BEDROOMS)
            }

            get(
                "/msa-codes/{$MSA_CODE_PARAM}/metrics-summary",
                {
                    tags = listOf(MSA_TAG)
                    summary = "MSA Rent metrics"
                    geoMetricsBaseParameterDoc {
                        pathParameter<String>(MSA_CODE_PARAM) {
                            required = true
                        }
                    }
                    response {
                        HttpStatusCode.OK to { body<MSARentSummary>() }
                    }
                },
            ) {
                getMetrics(setOf(call.getStringPathParameterOrFail(MSA_CODE_PARAM)), IdType.MSA, AggregatedMetricType.BY_ID)
            }
        }
    }

    private fun RouteConfig.geoMetricsBaseParameterDoc(block: RequestConfig.() -> Unit = {}) =
        request {
            block()
            queryParameter<LocalDate>(DATE_FROM_PARAM_NAME) {
                required = false
                description = DATE_FROM_DESC
            }
            queryParameter<LocalDate>(DATE_TO_PARAM_NAME) {
                required = false
                description = DATE_TO_DESC
            }
        }

    private suspend fun RoutingContext.getMetrics(
        ids: Set<String>,
        idType: IdType,
        metricType: AggregatedMetricType,
    ) {
        call.respond(
            getGeoMetricsHandler(
                GetGeoMetricsHandler.Input(
                    ids = ids,
                    idType = idType,
                    dateFrom = call.getLocalDateQueryParameter(DATE_FROM_PARAM_NAME),
                    dateTo = call.getLocalDateQueryParameter(DATE_TO_PARAM_NAME),
                    metricType = metricType,
                ),
            ).first(),
        )
    }
}
