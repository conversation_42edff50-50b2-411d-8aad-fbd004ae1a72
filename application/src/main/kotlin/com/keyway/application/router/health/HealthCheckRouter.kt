package com.keyway.application.router.health

import com.keyway.adapters.dtos.health.HealthCheckResponse
import com.keyway.adapters.handlers.HealthCheckHandler
import com.keyway.application.router.Router
import io.github.smiley4.ktoropenapi.get
import io.github.smiley4.ktoropenapi.route
import io.ktor.http.HttpStatusCode
import io.ktor.server.response.respond
import io.ktor.server.routing.Routing
import org.slf4j.LoggerFactory

class HealthCheckRouter(
    private val healthCheckHandler: HealthCheckHandler,
) : Router {
    private val logger = LoggerFactory.getLogger(this.javaClass)

    override fun setUpRoutes(routing: Routing) {
        routing.route {
            get(
                "/health",
                {
                    tags = listOf("health")
                    summary = "Health checker"
                    description = """Allows the infrastructure to check the health status of the service. 
        This endpoint doesn't verify database connections or another third party service status."""
                    response {
                        HttpStatusCode.OK to { body<HealthCheckResponse>() }
                    }
                },
            ) {
                call.respond(healthCheckHandler())
            }
        }
    }
}
