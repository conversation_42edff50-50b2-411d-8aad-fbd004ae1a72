package com.keyway.application.configuration.model

import com.keyway.kommons.aws.config.AwsConfig
import com.keyway.kommons.aws.config.SqsConfig
import com.keyway.kommons.db.configuration.DatabaseConfig
import com.keyway.kommons.sqs.configuration.SqsQueueConfig

data class Configuration(
    val serviceName: String,
    val saveGroupedRentSqsDelay: Long,
    val enableSqsConsumers: <PERSON><PERSON><PERSON>,
    val awsDatabaseServiceName: String,
    val system: SystemConfig,
    val datadogConfig: CustomDatadogConfig,
    val dataBaseConfig: DatabaseConfig,
    val awsConfig: AwsConfig,
    val sqsConfig: SqsConfig,
    val publishQueue: String,
)

// TODO: MOVE TO kommons-aws
fun SqsConfig.findQueueConfiguration(queueConfigKey: String) =
    queueConfigs
        .first {
            it.key == queueConfigKey
        }.let { queueConfiguration ->
            SqsQueueConfig(
                queueName = queueConfiguration.name,
                waitTimeSeconds = queueConfiguration.waitTimeSeconds,
                workers = queueConfiguration.workers,
                maxNumberOfMessages = queueConfiguration.maxNumberOfMessages,
                visibilityTimeout = queueConfiguration.visibilityTimeout,
            )
        }
