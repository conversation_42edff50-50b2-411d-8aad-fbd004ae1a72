CREATE TABLE "unit_rent_data_event" (
    property_id             VARCHAR NOT NULL,
    unit_id                 VARCHAR NOT NULL,
    record_id               VARCHAR,
    record_source           VARCHAR NOT NULL,
    zip_code                VARCHAR NOT NULL,
    msa_code                VARCHAR NOT NULL,
    unit_square_footage     NUMERIC,
    bedrooms                INT NOT NULL,
    bathrooms               NUMERIC,
    floor_plan              VARCHAR,
    available_in            DATE,
    rent                    NUMERIC,
    rent_deposit            NUMERIC,
    record_date             DATE NOT NULL,
    error_type              VARCHAR NOT NULL,
    created_at              TIMESTAMP(3) WITH TIME ZONE NOT NULL,
    updated_at              TIMESTAMP(3) WITH TIME ZONE NOT NULL,

    constraint unit_rent_data_event_pk_id primary key (property_id, unit_id)
);