ALTER TABLE unit_effective_rent DROP CONSTRAINT fk_unit_rent_listing_id;

ALTER TABLE unit_effective_rent RENAME COLUMN unit_rent_listing_id TO rent_listing_id;

ALTER TABLE unit_effective_rent RENAME TO effective_rent;

ALTER TABLE effective_rent
ADD CONSTRAINT fk_rent_listing_id FOREIGN KEY (rent_listing_id) REFERENCES rent_listing(id);

ALTER TABLE effective_rent DROP CONSTRAINT unit_effective_rent_unit_rent_listing_id_date_from_key;
ALTER TABLE effective_rent ADD CONSTRAINT effective_rent_rent_listing_id_date_from_key UNIQUE (rent_listing_id, date_from);
