create or replace procedure create_monthly_rent_views()
    language plpgsql
as
$$

DECLARE
    start_date DATE;
    end_date DATE;
    current_date_2 DATE;
    view_name TEXT;

BEGIN
    SELECT   date_trunc('month', MIN(date_from))::date INTO start_date FROM rent_listing;

    current_date_2 := start_date;
    end_date :=  CURRENT_DATE + INTERVAL '1 month';


    WHILE current_date_2 <= end_date LOOP
        view_name := format('rent_listing_by_%s_%s',
                            TO_CHAR(current_date_2, 'YYYY'),
                            TO_CHAR(current_date_2, 'MM'));


            RAISE NOTICE 'Test %',  view_name;


        -- Check if the view already exists
        IF NOT EXISTS (
            SELECT 1 FROM pg_matviews
            WHERE matviewname = view_name
        ) THEN

        CALL  create_monthly_rent_listing_view(view_name, current_date_2);
        CALL  add_index_to_created_view(view_name);

        EXECUTE 'ALTER MATERIALIZED VIEW ' || quote_ident(view_name) || ' OWNER TO rent;';

            -- Log and execute
        RAISE NOTICE 'Creating materialized view: %', view_name;

        ELSE
            RAISE NOTICE 'View % already exists. Skipping.', view_name;

        END IF;

        view_name := format('effective_rent_by_%s_%s',
                                            TO_CHAR(current_date_2, 'YYYY'),
                                            TO_CHAR(current_date_2, 'MM'));
        -- Check if the view already exists
        IF NOT EXISTS (
            SELECT 1 FROM pg_matviews
                    WHERE matviewname = view_name
        ) THEN

        CALL  create_monthly_effective_rent_view(view_name, current_date_2);
        CALL  add_index_to_created_view(view_name);

        EXECUTE 'ALTER MATERIALIZED VIEW ' || quote_ident(view_name) || ' OWNER TO rent;';

         -- Log and execute
        RAISE NOTICE 'Creating materialized view: %', view_name;

        ELSE

        RAISE NOTICE 'View % already exists. Skipping.', view_name;

        END IF;

        -- Move to the next month
        current_date_2 := current_date_2 + INTERVAL '1 month';
    END LOOP;

END$$;