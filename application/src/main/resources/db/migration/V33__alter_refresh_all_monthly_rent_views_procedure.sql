CREATE OR REPLACE PROCEDURE refresh_materialized_views(IN view_pattern text)
    LANGUAGE plpgsql
AS
$$
DECLARE
    matview_name TEXT;
    start_time TIMESTAMP;
    end_time TIMESTAMP;
    duration INTERVAL;
    total_start_time TIMESTAMP;
    total_end_time TIMESTAMP;
    total_duration INTERVAL;
    view_count INTEGER := 0;
    final_pattern TEXT;
BEGIN
    -- Auto-append % if not present for convenience
    final_pattern := CASE
                         WHEN view_pattern LIKE '%\%%' ESCAPE '\' THEN view_pattern
                         ELSE view_pattern || '%'
        END;
    total_start_time := clock_timestamp();
    RAISE NOTICE 'Starting refresh of materialized views matching pattern: %', final_pattern;
    RAISE NOTICE 'Process started at: %', total_start_time;
    FOR matview_name IN
        SELECT matviewname
        FROM pg_matviews
        WHERE matviewname ILIKE final_pattern
        ORDER BY matviewname
        LOOP
            view_count := view_count + 1;
            start_time := clock_timestamp();
            RAISE NOTICE '[%] Starting refresh of: %', view_count, matview_name;
            EXECUTE 'REFRESH MATERIALIZED VIEW ' || quote_ident(matview_name);
            end_time := clock_timestamp();
            duration := end_time - start_time;
            RAISE NOTICE '[%] Completed refresh of: %', view_count, matview_name;
            RAISE NOTICE '[%] Duration: % (% seconds)', view_count, duration, EXTRACT(EPOCH FROM duration);
            RAISE NOTICE '----------------------------------------';

            -- For concurrent refresh (when indexes exist):
            -- EXECUTE 'REFRESH MATERIALIZED VIEW CONCURRENTLY ' || quote_ident(matview_name);
        END LOOP;

    total_end_time := clock_timestamp();
    total_duration := total_end_time - total_start_time;

    RAISE NOTICE 'Total views refreshed: %', view_count;


    RAISE NOTICE 'Process completed at: %', total_end_time;

    IF view_count = 0 THEN
        RAISE NOTICE 'WARNING: No materialized views found matching pattern: %', final_pattern;
    END IF;
END $$;