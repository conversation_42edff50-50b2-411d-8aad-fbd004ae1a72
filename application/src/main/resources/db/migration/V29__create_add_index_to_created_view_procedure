create or replace procedure add_index_to_created_view(IN view_name text)
    language plpgsql
as
$$
DECLARE
    property_id_index_name TEXT :='';
    zip_code_index_name TEXT :='';
    msa_code_index_name TEXT :='';
BEGIN

        RAISE NOTICE 'Creating indexes in view: %', view_name;


        property_id_index_name := 'idx_property_id_'|| view_name;
        zip_code_index_name := 'idx_zip_code_'|| view_name;
        msa_code_index_name := 'idx_msa_code_'|| view_name;


        EXECUTE '
                    CREATE INDEX ' || quote_ident(property_id_index_name) ||
                    ' ON ' || quote_ident(view_name) || ' (property_id)
                ';
                RAISE NOTICE 'Index % created.', property_id_index_name;

        EXECUTE '
                    CREATE INDEX ' || quote_ident(zip_code_index_name) ||
                    ' ON ' || quote_ident(view_name) || ' (zip_code)
                ';
                RAISE NOTICE 'Index % created.', zip_code_index_name;

        EXECUTE '
                CREATE INDEX ' || quote_ident(msa_code_index_name) ||
                ' ON ' || quote_ident(view_name) || ' (msa_code)
            ';
                RAISE NOTICE 'Index % created.', msa_code_index_name;

END $$;


