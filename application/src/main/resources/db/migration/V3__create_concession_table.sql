CREATE TABLE IF NOT EXISTS property_concession (
   id VARCHAR PRIMARY KEY,
   property_id VARCHAR NOT NULL,
   periodicity_amount NUMERIC,
   periodicity_duration VARCHAR,
   periodicity_recurrent BOOLEAN,
   benefit_type VARCHAR,
   benefit_amount_type VARCHAR,
   benefit_amount NUMERIC,
   benefit_category VARCHAR,
   requirement VARCHAR,
   date_from DATE NOT NULL,
   date_to DATE NOT NULL,
   deadline DATE,
   description VARCHAR NOT NULL,
   created_at TIMESTAMP(3) WITH TIME ZONE NOT NULL,
   updated_at TIMESTAMP(3) WITH TIME ZONE NOT NULL
);
