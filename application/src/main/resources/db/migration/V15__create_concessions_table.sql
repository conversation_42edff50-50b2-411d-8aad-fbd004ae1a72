CREATE TABLE IF NOT EXISTS concessions (
    id VARCHAR PRIMARY KEY,
    property_id VARCHAR NOT NULL,
    text TEXT NOT NULL,
    date_from DATE NOT NULL,
    date_to DATE NOT NULL,
    created_at TIMESTAMP(3) WITH TIME ZONE NOT NULL,
    updated_at TIMESTAMP(3) WITH TIME ZONE NOT NULL,

    UNIQUE (property_id, text, date_from)
);

CREATE TABLE IF NOT EXISTS concessions_benefits (
    id VARCHAR PRIMARY KEY,
    concession_id VARCHAR NOT NULL REFERENCES concessions(id),
    type VARCHAR NOT NULL ,
    benefit VARCHAR,
    deadline DATE,
    amount_type VARCHAR,
    amount_value NUMERIC,
    periodicity_duration VARCHAR,
    periodicity_amount NUMERIC,
    periodicity_recurrent BOOLEAN,
    requirements jsonb NOT NULL,
    created_at TIMESTAMP(3) WITH TIME ZONE NOT NULL,
    updated_at TIMESTAMP(3) WITH TIME ZONE NOT NULL
);

CREATE INDEX idx_concessions_property_id ON concessions(property_id);

CREATE INDEX IF NOT EXISTS idx_concession_property_dates
    ON concessions (property_id, date_from, date_to);

CREATE INDEX IF NOT EXISTS idx_concession_benefits_id_by_type
    ON concessions_benefits (concession_id, type);

CREATE INDEX IF NOT EXISTS idx_concession_benefits_id_by_benefit
    ON concessions_benefits (concession_id, benefit);
