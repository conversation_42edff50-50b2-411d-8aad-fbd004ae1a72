CREATE TABLE unit_effective_rent (
     id VARCHAR NOT NULL,
     unit_rent_listing_id VARCHAR NOT NULL,
     concession_ids JSONB NOT NULL,
     date_from DATE NOT NULL,
     date_to DATE NOT NULL,
     rent NUMERIC NOT NULL,
     rent_deposit NUMERIC,
     concessions VARCHAR NOT NULL,
     created_at TIMESTAMP(3) WITH TIME ZONE NOT NULL,
     update_at TIMESTAMP(3) WITH TIME ZONE NOT NULL,
     UNIQUE (unit_rent_listing_id, date_from),
     CONSTRAINT fk_unit_rent_listing_id FOREIGN KEY (unit_rent_listing_id) REFERENCES unit_rent_listing(id)
);