create table if not exists historic_rent_listing
(
    id                  varchar                     not null
        constraint historic_unit_rent_listing_pkey
            primary key,
    property_id         varchar                     not null,
    type_id             varchar                     not null,
    rent                numeric                     not null,
    date_from           date                        not null,
    date_to             date                        not null,
    record_source       varchar                     not null,
    zip_code            varchar                     not null,
    msa_code            varchar                     not null,
    unit_square_footage numeric,
    bedrooms            integer                     not null,
    bathrooms           numeric,
    floor_plan          varchar,
    available_in        date,
    rent_deposit        numeric,
    created_at          timestamp(3) with time zone not null,
    updated_at          timestamp(3) with time zone not null,
    type                varchar                     not null,
    is_active           boolean                     not null,
    constraint historic_rent_listing_fr_key
        unique (property_id, type, type_id, record_source, date_from)
);

create index if not exists historic_rent_listing_index
    on historic_rent_listing (property_id, date_from, date_to, type);

create table if not exists historic_effective_rent
(
    id              varchar                     not null
        primary key,
    rent_listing_id varchar                     not null
        constraint fk_historic_rent_listing_id
            references historic_rent_listing,
    concession_ids  jsonb                       not null,
    date_from       date                        not null,
    date_to         date                        not null,
    rent            numeric                     not null,
    rent_deposit    numeric,
    concessions     varchar                     not null,
    created_at      timestamp(3) with time zone not null,
    update_at       timestamp(3) with time zone not null,
    is_active       boolean                     not null,
    constraint historic_rent_listing_unique_key
        unique (rent_listing_id, date_from)
);

create index if not exists historic_effective_rent_rent_listing_id_date_from_date_to_index
    on historic_effective_rent (rent_listing_id, date_from, date_to);