
ALTER TABLE effective_rent ALTER COLUMN property_id SET NOT NULL;
ALTER TABLE effective_rent ALTER COLUMN type SET NOT NULL;
ALTER TABLE effective_rent ALTER COLUMN type_id SET NOT NULL;
ALTER TABLE effective_rent ALTER COLUMN zip_code SET NOT NULL;
ALTER TABLE effective_rent ALTER COLUMN msa_code SET NOT NULL;
ALTER TABLE effective_rent ALTER COLUMN bedrooms SET NOT NULL;

create index if not exists effective_rent_property_id_date_from_date_to_type_index
    on effective_rent (property_id, date_from, date_to, type);

create index if not exists effective_rent_zip_code_date_from_index
    on effective_rent (zip_code, date_from);
