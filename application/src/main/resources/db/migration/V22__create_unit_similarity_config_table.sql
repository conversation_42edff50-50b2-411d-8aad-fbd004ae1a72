create table if not exists unit_similarity_calculator_config(
    organization_id varchar not null,
    sqft_weight numeric not null,
    bedroom_weight numeric not null,
    bathroom_weight numeric not null,
    amenities_weight numeric not null,
    sqft_sigma numeric not null,
    bedroom_sigma numeric not null,
    bathroom_sigma numeric not null,
    amenities_sigma numeric not null,
    reno_similarity numeric not null,
    min_amount_units_to_compare numeric not null,
    similarity_percentage_filter numeric not null,
    created_at timestamp default current_timestamp,
    constraint unit_similarity_config_unique_org unique (organization_id)
);

insert into unit_similarity_calculator_config(
    organization_id, sqft_weight, bedroom_weight, bathroom_weight,
    amenities_weight, sqft_sigma, bedroom_sigma, bathroom_sigma,
    amenities_sigma, reno_similarity, min_amount_units_to_compare,
    similarity_percentage_filter
) values (
          'DEFAULT', 0.5, 0.3, 0.15, 0.05, 75.0, 0.75, 0.5, 1.0, 0.7, 5, 0.6
         )