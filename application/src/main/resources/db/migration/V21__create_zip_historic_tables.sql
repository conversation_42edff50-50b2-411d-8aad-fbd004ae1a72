create table if not exists zip_historic_rent
(
    zip_code           varchar not null,
    msa_code           varchar not null,
    square_footage_avg numeric,
    rent_avg_psf       numeric,
    rent_median_psf    numeric,
    date_from          date    not null,
    month              int     not null,
    year               int     not null,
    constraint zip_historic_rent_un_key
        unique (zip_code, msa_code, date_from)
);

create index if not exists zip_historic_rent_index
    on zip_historic_rent (zip_code, date_from);