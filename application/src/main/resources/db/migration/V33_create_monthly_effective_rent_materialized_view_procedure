create or replace procedure create_monthly_effective_rent_view(IN view_name text, IN start_date date)
    language plpgsql
as
$$
DECLARE
    sql TEXT := '';

BEGIN
    -- Skip creation if view already exists
    IF EXISTS (
        SELECT 1 FROM pg_matviews WHERE matviewname = view_name
    ) THEN
        RAISE NOTICE 'View % already exists. Skipping.', view_name;
        RETURN;
    END IF;

    -- Start building SQL manually


     sql :=  'CREATE MATERIALIZED VIEW ' || quote_ident(view_name) || ' AS '||
        'WITH days_of_month AS (SELECT generate_series(''' ||start_date ||'''::date, ('''||
       start_date||'''::date + INTERVAL ''1 month'' - INTERVAL ''1 day'')::date,
   INTERVAL ''1 day'' )::date AS date_of_record) '  ||
          'select  property_id,
      msa_code,
      zip_code,
      bedrooms,
      sum(rent)                AS rent_sum,
      min(rent) As min_rent,
      max(rent) as max_rent,
      sum(unit_square_footage) AS sft_sum,
      min(unit_square_footage) as min_sft,
      max(unit_square_footage) as max_sft,
      count(*) as total_records,
      array_agg(type_id) as units,
      days_of_month.date_of_record - effective_rent.date_from + 1 AS days_listing_since_publish,
       days_of_month.date_of_record -
          GREATEST(date_trunc(''month''::text, days_of_month.date_of_record::timestamp with time zone)::date,
                   effective_rent.date_from) + 1                      AS day_listing_current_month,
           date_of_record
   from days_of_month , effective_rent
     where  date_of_record::date between date_from AND date_to
     and type =''UNIT'' and is_active = true
     group by property_id, msa_code, zip_code, bedrooms, date_of_record , date_from WITH NO DATA';


    -- Execute SQL
    RAISE NOTICE 'Creating materialized view: %', view_name;
    EXECUTE sql;

END $$;


