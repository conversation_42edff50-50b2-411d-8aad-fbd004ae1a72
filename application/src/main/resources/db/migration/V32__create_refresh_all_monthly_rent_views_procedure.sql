create or replace procedure refresh_all_monthly_views(IN view_name text)
    language plpgsql
as
$$
DECLARE
    matview_name TEXT;
BEGIN
    FOR matview_name IN
        SELECT matviewname
        FROM pg_matviews
        WHERE matviewname LIKE  view_name || '_%'
        ORDER BY matviewname
    LOOP
        RAISE NOTICE 'Refreshing: %', matview_name;
        EXECUTE 'REFRESH MATERIALIZED VIEW ' || quote_ident(matview_name);
        -- Only index in the materialized view do above
        -- EXECUTE 'REFRESH MATERIALIZED VIEW CONCURRENTLY ' || quote_ident(matview_name);
    END LOOP;
END $$;