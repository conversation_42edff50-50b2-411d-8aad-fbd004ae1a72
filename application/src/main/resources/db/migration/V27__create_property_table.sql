create table if not exists multifamily_properties
(
    id                              varchar                     not null
        constraint multifamily_properties_pk_id
            primary key,
    address                         varchar                     not null,
    city                            varchar                     not null,
    county                          varchar,
    zip_code                        bigint                      not null,
    state                           varchar                     not null,
    latitude                        numeric                     not null,
    longitude                       numeric                     not null,
    square_footage                  numeric,
    square_footage_per_unit         numeric,
    source_type                     varchar,
    tract_code                      bigint,
    construction_year               integer,
    renovation_year                 integer,
    unit_quantity                   integer,
    occupancy_percentage            numeric,
    is_active                       boolean,
    created_at                      timestamp(3) with time zone not null,
    updated_at                      timestamp(3) with time zone not null,
    property_amenities              character varying[],
    units_amenities                 character varying[],
    geolocation                     geography                   not null,
    quality_overall_score           numeric,
    stories                         integer,
    property_style                  varchar,
    housing_segment                 character varying[],
    has_affordable_units            boolean
);