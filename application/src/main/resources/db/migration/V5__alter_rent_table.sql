ALTER TABLE unit_rent_data_event RENAME COLUMN unit_id TO type_id;
ALTER TABLE unit_rent_listing RENAME COLUMN unit_id TO type_id;

ALTER TABLE unit_rent_data_event ADD COLUMN type VARCHAR;
ALTER TABLE unit_rent_listing ADD COLUMN type VARCHAR;

ALTER TABLE unit_rent_data_event RENAME TO rent_data_event;
UPDATE rent_data_event SET type = 'UNIT';

ALTER TABLE unit_rent_listing RENAME TO rent_listing;
UPDATE rent_listing SET type = 'UNIT';

UPDATE rent_data_event SET type = 'UNIT';
UPDATE rent_listing SET type = 'UNIT';

ALTER TABLE rent_data_event ALTER COLUMN type SET NOT NULL;
ALTER TABLE rent_listing ALTER COLUMN type SET NOT NULL;

ALTER TABLE rent_data_event DROP CONSTRAINT unit_rent_data_event_pk_id;
ALTER TABLE rent_data_event ADD CONSTRAINT rent_data_event_pk_id PRIMARY KEY (property_id, type_id, type);

ALTER TABLE rent_listing DROP CONSTRAINT unit_rent_listing_property_id_unit_id_record_source_date_fr_key;
ALTER TABLE rent_listing ADD CONSTRAINT rent_listing_property_id_type_id_record_source_date_fr_key
UNIQUE (property_id, type, type_id, record_source, date_from);
