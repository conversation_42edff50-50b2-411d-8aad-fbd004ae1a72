CREATE TABLE IF NOT EXISTS concessions_v2 (
    id VARCHAR PRIMARY KEY,
    property_id VARCHAR NOT NULL,
    concession_text TEXT NOT NULL,
    date_from DATE NOT NULL,
    date_to DATE NOT NULL,
    benefits jsonb NOT NULL,
    created_at TIMESTAMP(3) WITH TIME ZONE NOT NULL,
    updated_at TIMESTAMP(3) WITH TIME ZONE NOT NULL,

    UNIQUE (property_id, concession_text, date_from)
);


CREATE INDEX IF NOT EXISTS idx_concessions_v2_property_id ON concessions_v2(property_id);

CREATE INDEX IF NOT EXISTS idx_concession_v2_property_dates
    ON concessions_v2 (property_id, date_from, date_to);