service_name: "rent-api"

save_grouped_rent_sqs_delay: 0
enable_sqs_consumers: true
aws_database_service_name: "develop"

system:
  api_url: "http://localhost:8080"
  http_port: "8080"
  timeout: 10000

datadog_config:
  step_in_seconds: 20
  api_key: "datadog_api_key"

data_base_config:
  jdbc_url: "*************************************"
  username: "rent"
  password: "rent_password"
  driver_class_name: "org.postgresql.Driver"
  minimum_idle: "5"
  maximum_pool_size: "10"
  connection_timeout: "3000"
  idle_timeout: "60000"
  autocommit: true

aws_config:
  region: "us-east-1"
  account_id: "************"
  access_key: "access"
  secret_key: "secret"
  endpoint_override: "http://localstack:4566"

publish_queue: "AWS_SQS_LAST_SEEN_QUEUE"

sqs_config:
  queue_configs:
    - key: property_concessions_v2
      name: dev-rent-api-property_concessions_v2
      workers: 1
      wait_time_seconds: 1
      max_number_of_messages: 1
      visibility_timeout: 20
    - key: grouped_unit_rent_data
      name: dev-rent-api-grouped_rent_data
      workers: 1
      wait_time_seconds: 1
      max_number_of_messages: 1
      visibility_timeout: 20
    - key: background_rent_tasks
      name: dev-rent-background_rent_tasks
      workers: 1
      wait_time_seconds: 1
      max_number_of_messages: 1
      visibility_timeout: 20
    - key: property_units_data
      name: dev-rent-property_units_data
      workers: 1
      wait_time_seconds: 1
      max_number_of_messages: 1
      visibility_timeout: 20
    - key: property_data
      name: dev-rent-property_data
      workers: 1
      wait_time_seconds: 1
      max_number_of_messages: 1
      visibility_timeout: 20