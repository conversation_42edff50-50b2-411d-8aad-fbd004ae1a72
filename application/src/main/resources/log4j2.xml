<?xml version="1.0" encoding="UTF-8"?>
<Configuration>
    <Appenders>
        <Console name="STDOUT" target="SYSTEM_OUT">
<!--            <PatternLayout pattern="%d %highlight{| %5p |} %style{[trace_id : %X{trace_id}]}{cyan} %highlight{|} %style{%c{1.}}{yellow} %highlight{|} %style{%m%n}{bright white}"/>-->
            <PatternLayout pattern="%d %highlight{| %5p |} %style{[trace_id : %X{trace_id}]}{cyan} %style{[dd.trace_id: %X{dd.trace_id:-0}]}{cyan} %style{[dd.span_id: %X{dd.span_id:-0}]}{magenta} %highlight{|} %style{%c{1}}{yellow} %highlight{|} %style{%m%n}{bright white}"/>
        </Console>
    </Appenders>
    <Loggers>
        <Logger name="com.keyway" level="info" additivity="false">
            <AppenderRef ref="STDOUT"/>
        </Logger>
        <Root level="warn">
            <AppenderRef ref="STDOUT"/>
        </Root>
    </Loggers>
</Configuration>