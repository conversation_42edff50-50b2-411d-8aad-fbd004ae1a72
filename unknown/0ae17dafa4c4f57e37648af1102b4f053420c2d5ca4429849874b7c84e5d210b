CREATE OR REPLACE PROCEDURE create_monthly_rent_listing_view(IN view_name text, IN start_date date)
AS
$$
DECLARE
    sql TEXT := '';
BEGIN
    -- Skip creation if view already exists
    IF EXISTS (
        SELECT 1 FROM pg_matviews WHERE matviewname = view_name
    ) THEN
        RAISE NOTICE 'View % already exists. Skipping.', view_name;
        RETURN;
    END IF;

    -- Build dynamic SQL
    sql := 'CREATE MATERIALIZED VIEW ' || quote_ident(view_name) || ' AS ' ||
           'WITH days_of_month AS (SELECT generate_series(''' || start_date || '''::date, (''' ||
           start_date || '''::date + INTERVAL ''1 month'' - INTERVAL ''1 day'')::date, INTERVAL ''1 day'')::date AS date_of_record) ' ||
           'SELECT property_id, msa_code, zip_code, bedrooms, ' ||
           'sum(rent) AS rent_sum, min(rent) AS min_rent, max(rent) AS max_rent, ' ||
           'sum(unit_square_footage) AS sft_sum, min(unit_square_footage) AS min_sft, max(unit_square_footage) AS max_sft, ' ||
           'count(*) AS total_records, array_agg(type_id) AS units, ' ||
           'sum(days_of_month.date_of_record - rent_listing.date_from + 1) as  day_listing_since_publish, '
           'sum(rent_listing.date_to - rent_listing.date_from + 1) as  total_day_listing, '
           'date_of_record ' ||
           'FROM days_of_month, rent_listing ' ||
           'WHERE date_of_record::date BETWEEN date_from AND date_to ' ||
           'AND type = ''UNIT'' AND is_active = true ' ||
           'GROUP BY property_id, msa_code, zip_code, bedrooms, date_of_record WITH NO DATA';

    -- Execute SQL
    RAISE NOTICE 'Creating materialized view: %', view_name;
    EXECUTE sql;

END;
$$
LANGUAGE plpgsql;
