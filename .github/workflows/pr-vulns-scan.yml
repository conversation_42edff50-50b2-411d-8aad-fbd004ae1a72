name: Check for vulnerabilities

on:
  pull_request:
    branches:
      - master
      - develop

jobs:
  vulnerability-scan-dockerfiles:
    timeout-minutes: 10
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Run vulnerability scan for Image URIs
        id: vulnerability-scan-image-uris
        uses: unlockre/github-resources/actions/vulnerability-scan@main
        with:
          github_token: ${{ secrets.GITHUB_TOKEN }}
          severity_cutoff: medium
          only_fixed: true
          dockerfile: "Dockerfile"
          docker_args: |-
            {
            "GH_USERNAME":"${{ secrets.GH_USERNAME }}",
            "GH_TOKEN":"${{ secrets.GH_TOKEN }}"
            }
