package application.utils.module

import application.utils.clock.FixedClock
import application.utils.idgenerator.FixedIdGenerator
import application.utils.router.TestRouter
import application.utils.stub.StubMessagePublisher
import com.keyway.application.koin.ModuleConstants.ROUTES
import com.keyway.application.router.concession.GetConcessionTypesDistributionRouter
import com.keyway.application.router.concession.GetPropertiesConcessionsRouter
import com.keyway.application.router.health.HealthCheckRouter
import com.keyway.application.router.rent.GeoRentMetricsRouter
import com.keyway.application.router.rent.GetPropertiesLastListingRouter
import com.keyway.application.router.rent.GetPropertiesListingsRouter
import com.keyway.application.router.rent.GetPropertyLastListingRouter
import com.keyway.application.router.rent.GetPropertyListingsRouter
import com.keyway.application.router.rent.HistoricalRentRouter
import com.keyway.application.router.rent.RentMetricsRouter
import com.keyway.application.router.rent.RentSuggestionRouter
import com.keyway.application.utils.koin.createInstanceBy
import com.keyway.core.service.MessageBatchPublisher
import com.keyway.core.utils.DateUtils
import com.keyway.core.utils.IdGenerator
import com.keyway.kommons.aws.config.AwsConfig
import org.koin.core.qualifier.named
import org.koin.dsl.module
import software.amazon.awssdk.auth.credentials.AwsBasicCredentials
import software.amazon.awssdk.regions.Region
import software.amazon.awssdk.services.s3.S3Client
import software.amazon.awssdk.services.s3.S3Configuration
import java.net.URI
import java.time.Clock
import java.time.Instant
import java.time.ZoneOffset

object TestModule {
    val modules =
        module(createdAtStart = true) {
            single {
                FixedClock(Instant.parse("2023-08-11T01:01:00.00Z"), ZoneOffset.UTC)
            }

            single<Clock> {
                get<FixedClock>()
            }
            single { DateUtils.initialize(get()) }

            single {
                FixedIdGenerator()
            }

            single<IdGenerator> { get<FixedIdGenerator>() }

            single<S3Client> {
                get<AwsConfig>().let { awsConfig ->
                    S3Client
                        .builder()
                        .region(Region.of(awsConfig.region))
                        .serviceConfiguration(
                            S3Configuration
                                .builder()
                                .pathStyleAccessEnabled(true)
                                .build(),
                        ).credentialsProvider {
                            AwsBasicCredentials.create(awsConfig.accessKey, awsConfig.secretKey)
                        }.endpointOverride(URI.create(awsConfig.endpointOverride!!))
                        .build()
                }
            }

            single<MessageBatchPublisher> { StubMessagePublisher() }

            // Routers
            single(named(ROUTES)) {
                setOf(
                    createInstanceBy(::TestRouter),
                    createInstanceBy(::HealthCheckRouter),
                    createInstanceBy(::GetPropertyLastListingRouter),
                    createInstanceBy(::GetPropertyListingsRouter),
                    createInstanceBy(::GetPropertiesListingsRouter),
                    createInstanceBy(::GetPropertiesLastListingRouter),
                    createInstanceBy(::RentMetricsRouter),
                    createInstanceBy(::HistoricalRentRouter),
                    createInstanceBy(::GetPropertiesConcessionsRouter),
                    createInstanceBy(::GetConcessionTypesDistributionRouter),
                    createInstanceBy(::RentSuggestionRouter),
                    createInstanceBy(::GeoRentMetricsRouter),
                )
            }
        }
}
