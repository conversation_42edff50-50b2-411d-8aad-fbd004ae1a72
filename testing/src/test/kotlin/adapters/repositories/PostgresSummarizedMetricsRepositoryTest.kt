package adapters.repositories

import application.utils.base.BaseApplicationTest
import com.keyway.adapters.repositories.PostgresSummarizedMetricsRepository
import com.keyway.core.dto.query.metrics.IdType
import com.keyway.core.dto.query.metrics.MetricsFiltersQuery
import com.keyway.core.entities.EffectiveRent
import com.keyway.core.entities.Money
import com.keyway.core.entities.RentListing
import com.keyway.core.entities.RentListingType
import com.keyway.core.entities.metric.MetricType
import com.keyway.core.entities.property.PropertyUnit
import com.keyway.core.ports.repositories.EffectiveRentRepository
import com.keyway.core.ports.repositories.ListingsRepository
import com.keyway.core.ports.repositories.PropertyUnitRepository
import com.keyway.core.utils.DateUtils
import com.keyway.kommons.db.SqlClient
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.Assertions.*
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.koin.core.component.inject
import utils.MockedEntityFactory
import java.math.BigDecimal
import java.time.LocalDate
import java.util.UUID

class PostgresSummarizedMetricsRepositoryTest : BaseApplicationTest() {
    private val sqlClient: SqlClient by inject()
    private val listingRepo: ListingsRepository by inject()
    private val effectiveRentRepository: EffectiveRentRepository by inject()
    private val propUnitRepo: PropertyUnitRepository by inject()
    
    private lateinit var repository: PostgresSummarizedMetricsRepository
    private lateinit var zipCode: String
    private lateinit var testDate: LocalDate

    private val basePropertyId = "USFL-111111"
    private val compProperty1 = "USFL-222222"
    private val compProperty2 = "USFL-333333"

    @BeforeEach
    fun setup() {
        repository = PostgresSummarizedMetricsRepository(sqlClient)
        zipCode = "75217"
        testDate = LocalDate.parse("2023-08-03")

        // Setup test data
        setupTestData()
    }

    private fun setupTestData() {
        // Create property units using the same data as GetRentSuggestionHandlerTest
        val mockedUnits = mockedUnits()
        mockedUnits.forEach { unit -> propUnitRepo.saveOrUpdate(unit) }

        val compsUnits = mockedUnits.filter { unit ->
            unit.propertyId in setOf(compProperty1, compProperty2)
        }

        // Create rent listings
        val rentListings = compsUnits.map { unit ->
            RentListing(
                id = UUID.randomUUID().toString(),
                propertyId = unit.propertyId,
                type = RentListingType.UNIT,
                typeId = unit.unitId,
                rent = Money.of(1091 * unit.bedrooms),
                dateFrom = LocalDate.parse("2023-08-03"),
                dateTo = LocalDate.parse("2023-08-04"),
                recordSource = "apartments",
                zipCode = "75217",
                msaCode = "19100",
                unitSquareFootage = BigDecimal("895"),
                bedroomsQuantity = unit.bedrooms,
                bathroomsQuantity = unit.bathrooms,
                floorPlan = unit.floorPlan,
                availableIn = null,
                rentDeposit = Money.of(600),
                createdAt = DateUtils.now(),
                updateAt = DateUtils.now(),
            )
        }

        // Create effective rents
        val effectiveRents = rentListings.map {
            EffectiveRent.build(
                id = UUID.randomUUID().toString(),
                listing = it,
                concessionIds = listOf(),
                dateFrom = LocalDate.parse("2023-08-03"),
                dateTo = LocalDate.parse("2023-08-04"),
                rent = Money.of(500 * it.bedroomsQuantity),
                rentDeposit = null,
                concessions = "",
                createdAt = DateUtils.now(),
                updateAt = DateUtils.now(),
                isActive = true,
            )
        }

        // Save all data
        listingRepo.save(rentListings)
        effectiveRentRepository.save(effectiveRents)

        // Execute stored procedures to create materialized views
        executeStoredProcedures()
    }

    private fun executeStoredProcedures() {
        // For now, we'll skip the stored procedures execution
        // The materialized views would need to be created in a real database setup
        // This is a simplified version for testing
    }

    @Test
    fun `should aggregate metrics successfully`() = runBlocking {
        val query = MetricsFiltersQuery(
            ids = setOf(zipCode),
            idType = IdType.ZIP_CODE,
            dateFrom = testDate.minusDays(30),
            dateTo = testDate,
            type = MetricType.BY_ID
        )

        val result = repository.aggregateMetrics(query)

        assertNotNull(result)
        assertTrue(result.isNotEmpty())
        
        val firstMetric = result.first()
        assertNotNull(firstMetric.askingMetric)
        assertNotNull(firstMetric.askingMetric.askingRent)
        assertTrue(firstMetric.askingMetric.totalRecords > 0)
        assertTrue(firstMetric.askingMetric.totalProperties > 0)
    }

    @Test
    fun `should aggregate metrics by bedrooms`() = runBlocking {
        val query = MetricsFiltersQuery(
            ids = setOf(zipCode),
            idType = IdType.ZIP_CODE,
            dateFrom = testDate.minusDays(30),
            dateTo = testDate,
            type = MetricType.BEDROOMS
        )

        val result = repository.aggregateMetrics(query)

        assertNotNull(result)
        assertTrue(result.isNotEmpty())
        
        val firstMetric = result.first()
        assertNotNull(firstMetric)
        assertNotNull(firstMetric.totalProperties)
    }

    @Test
    fun `should build asking metrics query correctly for rent_listing`() {
        val query = MetricsFiltersQuery(
            ids = setOf("12345"),
            idType = IdType.ZIP_CODE,
            dateFrom = LocalDate.of(2023, 1, 1),
            dateTo = LocalDate.of(2023, 2, 28),
            type = MetricType.OVERALL
        )

        val sql = repository.buildAskingMetricsQuery("rent_listing", query)

        assertTrue(sql.contains("rent_listing_by_2023_01"))
        assertTrue(sql.contains("rent_listing_by_2023_02"))
        assertTrue(sql.contains("zip_code in ('12345')"))
        assertTrue(sql.contains("date_of_record BETWEEN '2023-01-01' AND '2023-02-28'"))
        assertTrue(sql.contains("UNION ALL"))
    }

    @Test
    fun `should build asking metrics query correctly for effective_rent`() {
        val query = MetricsFiltersQuery(
            ids = setOf("USFL-014695"),
            idType = IdType.PROPERTY,
            dateFrom = LocalDate.of(2023, 6, 1),
            dateTo = LocalDate.of(2023, 6, 30),
            type = MetricType.BEDROOMS
        )

        val sql = repository.buildAskingMetricsQuery("effective_rent", query)

        assertTrue(sql.contains("effective_rent_by_2023_06"))
        assertTrue(sql.contains("property_id in ('USFL-014695')"))
        assertTrue(sql.contains("bedrooms,"))
        assertTrue(sql.contains("GROUP BY bedrooms"))
    }

    private fun mockedUnits() = listOf(
        PropertyUnit(
            unitId = "100",
            propertyId = basePropertyId,
            squareFootage = BigDecimal("150"),
            bedrooms = 1,
            bathrooms = BigDecimal.ONE,
            floorPlan = "F1",
            renovated = false,
            renovationProbability = BigDecimal("0.11"),
            amenities = setOf("Pool", "BBQ", "Parking"),
        ),
        PropertyUnit(
            unitId = "200",
            propertyId = basePropertyId,
            squareFootage = BigDecimal("300"),
            bedrooms = 2,
            bathrooms = BigDecimal(2),
            floorPlan = "F2",
            renovated = false,
            renovationProbability = BigDecimal("0.11"),
            amenities = setOf("Pool", "BBQ", "Parking"),
        ),
        // EQUALS
        PropertyUnit(
            unitId = "1",
            propertyId = compProperty1,
            squareFootage = BigDecimal("150"),
            bedrooms = 1,
            bathrooms = BigDecimal.ONE,
            floorPlan = "F1",
            renovated = false,
            renovationProbability = BigDecimal("0.11"),
            amenities = setOf("Pool", "BBQ", "Parking"),
        ),
        // LESS SQFT SAME UNIT MIX
        PropertyUnit(
            unitId = "11",
            propertyId = compProperty1,
            squareFootage = BigDecimal("100"),
            bedrooms = 1,
            bathrooms = BigDecimal.ONE,
            floorPlan = "F1",
            renovated = false,
            renovationProbability = BigDecimal("0.11"),
            amenities = setOf("Pool"),
        ),
        // DIFFERENT RENO STATE
        PropertyUnit(
            unitId = "12",
            propertyId = compProperty1,
            squareFootage = BigDecimal("100"),
            bedrooms = 1,
            bathrooms = BigDecimal.ONE,
            floorPlan = "F1",
            renovated = true,
            renovationProbability = BigDecimal("0.99"),
            amenities = setOf("Pool"),
        ),
        // BIGGER IN UNIT MIX AND SQFT
        PropertyUnit(
            unitId = "2",
            propertyId = compProperty1,
            squareFootage = BigDecimal("200"),
            bedrooms = 2,
            bathrooms = BigDecimal("1.5"),
            floorPlan = "F2",
            renovated = false,
            renovationProbability = BigDecimal("0.11"),
            amenities = setOf("Pool"),
        ),
        // BIGGER IN UNIT MIX BUT EQUALS IN SQFT
        PropertyUnit(
            unitId = "22",
            propertyId = compProperty1,
            squareFootage = BigDecimal("100"),
            bedrooms = 2,
            bathrooms = BigDecimal("1.5"),
            floorPlan = "F2",
            renovated = false,
            renovationProbability = BigDecimal("0.11"),
            amenities = setOf("Pool"),
        ),
        PropertyUnit(
            unitId = "3",
            propertyId = compProperty1,
            squareFootage = BigDecimal("300"),
            bedrooms = 3,
            bathrooms = BigDecimal("2.5"),
            floorPlan = "F3",
            renovated = false,
            renovationProbability = BigDecimal("0.11"),
            amenities = setOf("Pool"),
        ),
        PropertyUnit(
            unitId = "201",
            propertyId = compProperty2,
            squareFootage = BigDecimal("300"),
            bedrooms = 2,
            bathrooms = BigDecimal(2),
            floorPlan = "Fx2",
            renovated = false,
            renovationProbability = BigDecimal("0.11"),
            amenities = setOf("Pool", "BBQ", "Parking"),
        ),
        PropertyUnit(
            unitId = "202",
            propertyId = compProperty2,
            squareFootage = BigDecimal("200"),
            bedrooms = 2,
            bathrooms = BigDecimal(2),
            floorPlan = "Fx2",
            renovated = false,
            renovationProbability = BigDecimal("0.11"),
            amenities = setOf("Pool", "BBQ", "Parking"),
        ),
        PropertyUnit(
            unitId = "203",
            propertyId = compProperty2,
            squareFootage = BigDecimal("200"),
            bedrooms = 2,
            bathrooms = BigDecimal(2),
            floorPlan = "Fx2",
            renovated = true,
            renovationProbability = BigDecimal("0.11"),
            amenities = setOf("Pool", "BBQ", "Parking"),
        ),
        PropertyUnit(
            unitId = "204",
            propertyId = compProperty2,
            squareFootage = BigDecimal("450"),
            bedrooms = 2,
            bathrooms = BigDecimal(2),
            floorPlan = "Fx2",
            renovated = false,
            renovationProbability = BigDecimal("0.11"),
            amenities = setOf("Pool", "BBQ", "Parking"),
        ),
        PropertyUnit(
            unitId = "205",
            propertyId = compProperty2,
            squareFootage = BigDecimal("300"),
            bedrooms = 3,
            bathrooms = BigDecimal(3),
            floorPlan = "Fx2",
            renovated = false,
            renovationProbability = BigDecimal("0.11"),
            amenities = setOf("Pool", "BBQ", "Parking"),
        ),
    )

    private fun getUnits(propertyId: String): List<PropertyUnit> {
        return listOf("9-305", "9-306", "9-307", "9-308", "9-309").map {
            PropertyUnit(
                propertyId = propertyId,
                unitId = it,
                squareFootage = BigDecimal.TEN,
                bedrooms = 1,
                bathrooms = BigDecimal.ONE,
                floorPlan = "CC",
                amenities = setOf("NONE"),
                renovationProbability = BigDecimal(0.9),
                renovated = true,
            )
        }
    }

    private fun getListings(propertyId: String): List<RentListing> {
        return (1..5).map { index ->
            RentListing(
                id = UUID.randomUUID().toString(),
                propertyId = propertyId,
                type = RentListingType.UNIT,
                typeId = "9-30$index",
                rent = Money.of("${2000 + index * 100}"),
                dateFrom = testDate.minusDays(30),
                dateTo = testDate,
                recordSource = "apartments",
                zipCode = "33009",
                msaCode = "35620",
                unitSquareFootage = BigDecimal("${1000 + index * 50}"),
                bedroomsQuantity = if (index <= 2) 1 else 2,
                bathroomsQuantity = BigDecimal("1"),
                floorPlan = "A$index",
                availableIn = null,
                rentDeposit = Money.of("600"),
                createdAt = DateUtils.now(),
                updateAt = DateUtils.now(),
            )
        }
    }
}
