package adapters.repositories

import application.utils.base.BaseApplicationTest
import com.keyway.core.dto.query.metrics.IdType
import com.keyway.core.dto.query.metrics.MetricsFiltersQuery
import com.keyway.core.entities.Money
import com.keyway.core.entities.RentListing
import com.keyway.core.entities.RentListingType
import com.keyway.core.entities.metric.MetricType
import com.keyway.core.entities.property.PropertyUnit
import com.keyway.core.ports.repositories.EffectiveRentRepository
import com.keyway.core.ports.repositories.ListingsRepository
import com.keyway.core.ports.repositories.PropertyUnitRepository
import com.keyway.core.ports.repositories.SummarizedMetricsRepository
import com.keyway.kommons.db.transaction.datasource.TransactionalDataSource
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.koin.core.component.inject
import utils.MockedEntityFactory
import java.math.BigDecimal
import java.time.LocalDate
import kotlin.test.assertEquals
import kotlin.test.assertNotNull
import kotlin.test.assertTrue

class PostgresSummarizedMetricsRepositoryTest : BaseApplicationTest() {
    private val summarizedMetricsRepo: SummarizedMetricsRepository by inject()
    private val listingRepo: ListingsRepository by inject()
    private val effectiveRentRepository: EffectiveRentRepository by inject()
    private val propUnitRepo: PropertyUnitRepository by inject()
    private val dataSource: TransactionalDataSource by inject()

    private lateinit var propertyId: String
    private lateinit var testDate: LocalDate

    @BeforeEach
    fun setup() {
        propertyId = "USFL-014695"
        testDate = LocalDate.now()

        // Setup test data
        getUnits(propertyId).forEach(propUnitRepo::saveOrUpdate)
        getListings(propertyId).forEach { listing ->
            listingRepo.save(listing)
            effectiveRentRepository.save(
                MockedEntityFactory.buildEffectiveRent(
                    listing = listing,
                    rent = listing.rent,
                    dateFrom = listing.dateFrom,
                    dateTo = listing.dateTo,
                ),
            )
        }

        // Create and refresh materialized views required by PostgresSummarizedMetricsRepository
        createAndRefreshViews()
    }

    @Test
    fun `should aggregate metrics by property ID`() {
        val metrics =
            runBlocking {
                summarizedMetricsRepo.aggregateMetrics(
                    MetricsFiltersQuery(
                        ids = setOf(propertyId),
                        idType = IdType.PROPERTY,
                        dateFrom = testDate,
                        dateTo = testDate,
                        type = MetricType.BY_ID,
                    ),
                )
            }

        assertEquals(1, metrics.size)
        val metric = metrics.first()
        assertEquals(propertyId, metric.id.replace("[", "").replace("]", ""))
        assertEquals(IdType.PROPERTY, metric.metricType)
        assertTrue(metric.totalRecords > 0)
        assertTrue(metric.totalUnits > 0)
        assertTrue(metric.totalProperties > 0)
        assertNotNull(metric.askingRent)
        assertNotNull(metric.effectiveRent)
        assertNotNull(metric.squareFootage)
    }

    @Test
    fun `should aggregate metrics by bedrooms`() {
        val metrics =
            runBlocking {
                summarizedMetricsRepo.aggregateMetrics(
                    MetricsFiltersQuery(
                        ids = setOf(propertyId),
                        idType = IdType.PROPERTY,
                        dateFrom = testDate,
                        dateTo = testDate,
                        type = MetricType.BEDROOMS,
                    ),
                )
            }

        assertTrue(metrics.isNotEmpty())
        // Should have metrics grouped by bedroom count
        val bedroomCounts = metrics.mapNotNull { it.bedrooms }.distinct()
        assertTrue(bedroomCounts.isNotEmpty())

        metrics.forEach { metric ->
            assertNotNull(metric.bedrooms)
            assertTrue(metric.totalRecords > 0)
            assertNotNull(metric.askingRent)
            assertNotNull(metric.effectiveRent)
        }
    }

    @Test
    fun `should handle MSA ID type`() {
        val msaCode = "19100"
        val metrics =
            runBlocking {
                summarizedMetricsRepo.aggregateMetrics(
                    MetricsFiltersQuery(
                        ids = setOf(msaCode),
                        idType = IdType.MSA,
                        dateFrom = testDate,
                        dateTo = testDate,
                        type = MetricType.BY_ID,
                    ),
                )
            }

        // Should return results for MSA-based queries
        assertTrue(metrics.isNotEmpty())
        val metric = metrics.first()
        assertEquals(IdType.MSA, metric.metricType)
    }

    @Test
    fun `should handle ZIP_CODE ID type`() {
        val zipCode = "75217"
        val metrics =
            runBlocking {
                summarizedMetricsRepo.aggregateMetrics(
                    MetricsFiltersQuery(
                        ids = setOf(zipCode),
                        idType = IdType.ZIP_CODE,
                        dateFrom = testDate,
                        dateTo = testDate,
                        type = MetricType.BY_ID,
                    ),
                )
            }

        // Should return results for ZIP code-based queries
        assertTrue(metrics.isNotEmpty())
        val metric = metrics.first()
        assertEquals(IdType.ZIP_CODE, metric.metricType)
    }

    @Test
    fun `should handle date range filtering`() {
        val dateFrom = testDate.minusDays(7)
        val dateTo = testDate.plusDays(7)

        val metrics =
            runBlocking {
                summarizedMetricsRepo.aggregateMetrics(
                    MetricsFiltersQuery(
                        ids = setOf(propertyId),
                        idType = IdType.PROPERTY,
                        dateFrom = dateFrom,
                        dateTo = dateTo,
                        type = MetricType.BY_ID,
                    ),
                )
            }

        assertTrue(metrics.isNotEmpty())
        val metric = metrics.first()
        assertTrue(metric.totalRecords > 0)
    }

    @Test
    fun `should return empty list for non-existent property`() {
        val nonExistentPropertyId = "NONEXISTENT-123"

        val metrics =
            runBlocking {
                summarizedMetricsRepo.aggregateMetrics(
                    MetricsFiltersQuery(
                        ids = setOf(nonExistentPropertyId),
                        idType = IdType.PROPERTY,
                        dateFrom = testDate,
                        dateTo = testDate,
                        type = MetricType.BY_ID,
                    ),
                )
            }

        assertTrue(metrics.isEmpty())
    }

    @Test
    fun `should handle multiple metric types`() {
        val metricTypes = listOf(MetricType.BY_ID, MetricType.BEDROOMS)

        metricTypes.forEach { metricType ->
            val metrics =
                runBlocking {
                    summarizedMetricsRepo.aggregateMetrics(
                        MetricsFiltersQuery(
                            ids = setOf(propertyId),
                            idType = IdType.PROPERTY,
                            dateFrom = testDate,
                            dateTo = testDate,
                            type = metricType,
                        ),
                    )
                }

            assertTrue(metrics.isNotEmpty(), "Should have metrics for $metricType")
        }
    }

    @Test
    fun `should handle effective rent data correctly`() {
        val metrics =
            runBlocking {
                summarizedMetricsRepo.aggregateMetrics(
                    MetricsFiltersQuery(
                        ids = setOf(propertyId),
                        idType = IdType.PROPERTY,
                        dateFrom = testDate,
                        dateTo = testDate,
                        type = MetricType.BY_ID,
                    ),
                )
            }

        assertTrue(metrics.isNotEmpty())
        val metric = metrics.first()

        // Verify that both asking and effective rent are populated
        assertNotNull(metric.askingRent)
        assertNotNull(metric.effectiveRent)

        // Verify rent metrics have valid values
        assertTrue(metric.askingRent.min > BigDecimal.ZERO)
        assertTrue(metric.askingRent.max > BigDecimal.ZERO)
        assertTrue(metric.askingRent.average > BigDecimal.ZERO)

        assertTrue(metric.effectiveRent.min > BigDecimal.ZERO)
        assertTrue(metric.effectiveRent.max > BigDecimal.ZERO)
        assertTrue(metric.effectiveRent.average > BigDecimal.ZERO)
    }

    @Test
    fun `should validate square footage metrics`() {
        val metrics =
            runBlocking {
                summarizedMetricsRepo.aggregateMetrics(
                    MetricsFiltersQuery(
                        ids = setOf(propertyId),
                        idType = IdType.PROPERTY,
                        dateFrom = testDate,
                        dateTo = testDate,
                        type = MetricType.BY_ID,
                    ),
                )
            }

        assertTrue(metrics.isNotEmpty())
        val metric = metrics.first()

        assertNotNull(metric.squareFootage)
        assertTrue(metric.squareFootage.min > BigDecimal.ZERO)
        assertTrue(metric.squareFootage.max > BigDecimal.ZERO)
        assertTrue(metric.squareFootage.average > BigDecimal.ZERO)

        // Square footage should be reasonable (between 500 and 2000 sqft)
        assertTrue(metric.squareFootage.min >= BigDecimal("500"))
        assertTrue(metric.squareFootage.max <= BigDecimal("2000"))
    }

    @Test
    fun `should handle concurrent requests correctly`() {
        val queries =
            (1..5).map {
                MetricsFiltersQuery(
                    ids = setOf(propertyId),
                    idType = IdType.PROPERTY,
                    dateFrom = testDate,
                    dateTo = testDate,
                    type = MetricType.BY_ID,
                )
            }

        val results =
            runBlocking {
                queries.map { query ->
                    summarizedMetricsRepo.aggregateMetrics(query)
                }
            }

        // All requests should return the same results
        assertTrue(results.all { it.isNotEmpty() })
        val firstResult = results.first()
        results.forEach { result ->
            assertEquals(firstResult.size, result.size)
            assertEquals(firstResult.first().totalRecords, result.first().totalRecords)
        }
    }

    private fun createAndRefreshViews() {
        dataSource.connection.use { connection ->
            connection.createStatement().use { statement ->
                // Create monthly rent views
                statement.execute("call public.create_monthly_rent_views()")

                // Refresh rent listing materialized views
                statement.execute("call public.refresh_materialized_views('rent_listing_%')")

                // Refresh effective rent materialized views
                statement.execute("call public.refresh_materialized_views('effective_rent_%')")
            }
        }
    }

    private fun getUnits(propertyId: String) =
        listOf(
            PropertyUnit(
                propertyId = propertyId,
                unitId = "101",
                squareFootage = BigDecimal("850"),
                bedrooms = 1,
                bathrooms = BigDecimal.ONE,
                floorPlan = "A1",
                amenities = setOf("NONE"),
                renovationProbability = BigDecimal("0.3"),
                renovated = false,
            ),
            PropertyUnit(
                propertyId = propertyId,
                unitId = "102",
                squareFootage = BigDecimal("950"),
                bedrooms = 2,
                bathrooms = BigDecimal("1.5"),
                floorPlan = "B1",
                amenities = setOf("BALCONY"),
                renovationProbability = BigDecimal("0.7"),
                renovated = true,
            ),
            PropertyUnit(
                propertyId = propertyId,
                unitId = "103",
                squareFootage = BigDecimal("1200"),
                bedrooms = 3,
                bathrooms = BigDecimal("2.0"),
                floorPlan = "C1",
                amenities = setOf("BALCONY", "FIREPLACE"),
                renovationProbability = BigDecimal("0.5"),
                renovated = false,
            ),
        )

    private fun getListings(propertyId: String): List<RentListing> =
        getUnits(propertyId)
            .map { unit ->
                MockedEntityFactory.buildRentListing(
                    propertyId = propertyId,
                    typeId = unit.unitId,
                    type = RentListingType.UNIT,
                    dateFrom = testDate,
                    dateTo = testDate,
                    rent = Money.of(1000 + (unit.bedrooms * 200)), // Vary rent by bedroom count
                    unitSquareFootage = unit.squareFootage,
                    bedroomsQuantity = unit.bedrooms,
                    bathroomsQuantity = unit.bathrooms,
                    floorPlan = unit.floorPlan,
                )
            }.plus(
                // Add some floor plan listings
                listOf(
                    MockedEntityFactory.buildRentListing(
                        propertyId = propertyId,
                        typeId = "A1",
                        type = RentListingType.FLOOR_PLAN,
                        dateFrom = testDate,
                        dateTo = testDate,
                        rent = Money.of(1100),
                        bedroomsQuantity = 1,
                        bathroomsQuantity = BigDecimal.ONE,
                        floorPlan = "A1",
                    ),
                    MockedEntityFactory.buildRentListing(
                        propertyId = propertyId,
                        typeId = "B1",
                        type = RentListingType.FLOOR_PLAN,
                        dateFrom = testDate,
                        dateTo = testDate,
                        rent = Money.of(1300),
                        bedroomsQuantity = 2,
                        bathroomsQuantity = BigDecimal("1.5"),
                        floorPlan = "B1",
                    ),
                ),
            )
}
