package adapters.repositories

import application.utils.base.BaseApplicationTest
import com.keyway.adapters.repositories.PostgresSummarizedMetricsRepository
import com.keyway.core.dto.query.metrics.IdType
import com.keyway.core.dto.query.metrics.MetricsFiltersQuery
import com.keyway.core.entities.EffectiveRent
import com.keyway.core.entities.Money
import com.keyway.core.entities.RentListing
import com.keyway.core.entities.RentListingType
import com.keyway.core.entities.metric.MetricType
import com.keyway.core.entities.property.PropertyUnit
import com.keyway.core.ports.repositories.EffectiveRentRepository
import com.keyway.core.ports.repositories.ListingsRepository
import com.keyway.core.ports.repositories.PropertyUnitRepository
import com.keyway.core.utils.DateUtils
import com.keyway.kommons.db.SqlClient
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.Assertions.*
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.koin.core.component.inject
import utils.MockedEntityFactory
import java.math.BigDecimal
import java.time.LocalDate
import java.util.UUID

class PostgresSummarizedMetricsRepositoryTest : BaseApplicationTest() {
    private val sqlClient: SqlClient by inject()
    private val listingRepo: ListingsRepository by inject()
    private val effectiveRentRepository: EffectiveRentRepository by inject()
    private val propUnitRepo: PropertyUnitRepository by inject()
    
    private lateinit var repository: PostgresSummarizedMetricsRepository
    private lateinit var propertyId: String
    private lateinit var testDate: LocalDate

    @BeforeEach
    fun setup() {
        repository = PostgresSummarizedMetricsRepository(sqlClient)
        propertyId = "USFL-014695"
        testDate = LocalDate.now()
        
        // Setup test data
        setupTestData()
    }

    private fun setupTestData() {
        // Create property units
        getUnits(propertyId).forEach(propUnitRepo::saveOrUpdate)
        
        // Create rent listings
        getListings(propertyId).forEach { listing ->
            listingRepo.save(listing)
            effectiveRentRepository.save(
                MockedEntityFactory.buildEffectiveRent(
                    listing = listing,
                    rent = listing.rent,
                    dateFrom = listing.dateFrom,
                    dateTo = listing.dateTo,
                )
            )
        }
    }

    @Test
    fun `should aggregate metrics successfully`() = runBlocking {
        val query = MetricsFiltersQuery(
            ids = setOf(propertyId),
            idType = IdType.PROPERTY,
            dateFrom = testDate.minusDays(30),
            dateTo = testDate,
            type = MetricType.OVERALL
        )

        val result = repository.aggregateMetrics(query)

        assertNotNull(result)
        assertTrue(result.isNotEmpty())
        
        val firstMetric = result.first()
        assertNotNull(firstMetric.askingMetric)
        assertNotNull(firstMetric.askingMetric.askingRent)
        assertTrue(firstMetric.askingMetric.totalRecords > 0)
        assertTrue(firstMetric.askingMetric.totalProperties > 0)
    }

    @Test
    fun `should aggregate metrics by bedrooms`() = runBlocking {
        val query = MetricsFiltersQuery(
            ids = setOf(propertyId),
            idType = IdType.PROPERTY,
            dateFrom = testDate.minusDays(30),
            dateTo = testDate,
            type = MetricType.BEDROOMS
        )

        val result = repository.aggregateMetrics(query)

        assertNotNull(result)
        assertTrue(result.isNotEmpty())
        
        val firstMetric = result.first()
        assertNotNull(firstMetric.askingMetric)
        assertNotNull(firstMetric.askingMetric.bedrooms)
    }

    @Test
    fun `should build asking metrics query correctly for rent_listing`() {
        val query = MetricsFiltersQuery(
            ids = setOf("12345"),
            idType = IdType.ZIP_CODE,
            dateFrom = LocalDate.of(2023, 1, 1),
            dateTo = LocalDate.of(2023, 2, 28),
            type = MetricType.OVERALL
        )

        val sql = repository.buildAskingMetricsQuery("rent_listing", query)

        assertTrue(sql.contains("rent_listing_by_2023_01"))
        assertTrue(sql.contains("rent_listing_by_2023_02"))
        assertTrue(sql.contains("zip_code in ('12345')"))
        assertTrue(sql.contains("date_of_record BETWEEN '2023-01-01' AND '2023-02-28'"))
        assertTrue(sql.contains("UNION ALL"))
    }

    @Test
    fun `should build asking metrics query correctly for effective_rent`() {
        val query = MetricsFiltersQuery(
            ids = setOf("USFL-014695"),
            idType = IdType.PROPERTY,
            dateFrom = LocalDate.of(2023, 6, 1),
            dateTo = LocalDate.of(2023, 6, 30),
            type = MetricType.BEDROOMS
        )

        val sql = repository.buildAskingMetricsQuery("effective_rent", query)

        assertTrue(sql.contains("effective_rent_by_2023_06"))
        assertTrue(sql.contains("property_id in ('USFL-014695')"))
        assertTrue(sql.contains("bedrooms,"))
        assertTrue(sql.contains("GROUP BY bedrooms"))
    }

    private fun getUnits(propertyId: String): List<PropertyUnit> {
        return listOf("9-305", "9-306", "9-307", "9-308", "9-309").map {
            PropertyUnit(
                propertyId = propertyId,
                unitId = it,
                squareFootage = BigDecimal.TEN,
                bedrooms = 1,
                bathrooms = BigDecimal.ONE,
                floorPlan = "CC",
                amenities = setOf("NONE"),
                renovationProbability = BigDecimal(0.9),
                renovated = true,
            )
        }
    }

    private fun getListings(propertyId: String): List<RentListing> {
        return (1..5).map { index ->
            RentListing(
                id = UUID.randomUUID().toString(),
                propertyId = propertyId,
                type = RentListingType.UNIT,
                typeId = "9-30$index",
                rent = Money.of("${2000 + index * 100}"),
                dateFrom = testDate.minusDays(30),
                dateTo = testDate,
                recordSource = "apartments",
                zipCode = "33009",
                msaCode = "35620",
                unitSquareFootage = BigDecimal("${1000 + index * 50}"),
                bedroomsQuantity = if (index <= 2) 1 else 2,
                bathroomsQuantity = BigDecimal("1"),
                floorPlan = "A$index",
                availableIn = null,
                rentDeposit = Money.of("600"),
                createdAt = DateUtils.now(),
                updateAt = DateUtils.now(),
            )
        }
    }
}
