package adapters.repositories

import application.utils.base.BaseApplicationTest
import com.keyway.core.dto.query.metrics.IdType
import com.keyway.core.entities.EffectiveRent
import com.keyway.core.entities.HistoricalPeriodicity
import com.keyway.core.entities.Money
import com.keyway.core.entities.RentListing
import com.keyway.core.entities.RentListingType
import com.keyway.core.entities.RentType
import com.keyway.core.entities.UnitCondition
import com.keyway.core.entities.property.PropertyUnit
import com.keyway.core.ports.repositories.EffectiveRentRepository
import com.keyway.core.ports.repositories.HistoricalRentRepository
import com.keyway.core.ports.repositories.ListingsRepository
import com.keyway.core.ports.repositories.PropertyUnitRepository
import com.keyway.core.utils.DateUtils
import com.keyway.kommons.db.transaction.datasource.TransactionalDataSource
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.Assertions
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.koin.core.component.inject
import java.math.BigDecimal
import java.time.LocalDate
import java.time.temporal.ChronoUnit
import java.util.UUID

class HistoricalRentRepositoryTest : BaseApplicationTest() {
    private val listingsRepository: ListingsRepository by inject()
    private val effectiveRentRepository: EffectiveRentRepository by inject()
    private val target: HistoricalRentRepository by inject()
    private val propUnitRepo: PropertyUnitRepository by inject()
    private val dataSource: TransactionalDataSource by inject()

    private val units = listOf<String>("9-305", "9-306", "9-307", "9-308", "9-309")
    private val zipCode = "33009"
    private val msaCode = "35620"

    @BeforeEach
    fun setUp() {
        var date = LocalDate.now().minusYears(1)
        for (x in 0..400) {
            val listing =
                getRentListing(
                    rent = Money.of("100") + Money.of(x),
                    dateTo = date,
                    dateFrom = date,
                )

            listingsRepository.save(listing).let {
                effectiveRentRepository.save(
                    EffectiveRent.build(
                        id = UUID.randomUUID().toString(),
                        rent = listing.rent,
                        rentDeposit = listing.rentDeposit,
                        dateTo = listing.dateTo,
                        dateFrom = listing.dateFrom,
                        listing = listing,
                        concessionIds = emptyList(),
                        concessions = "",
                        createdAt = listing.createdAt,
                        updateAt = listing.updateAt,
                    ),
                )
            }
            date = date.plusDays(1)
        }

        dataSource.connection.use { connection ->
            connection.createStatement().use { statement ->
                // Create monthly rent views
                statement.execute("call public.create_monthly_rent_views()")

                // Refresh rent listing materialized views
                statement.execute("call public.refresh_materialized_views('rent_listing_%')")

                // Refresh effective rent materialized views
                statement.execute("call public.refresh_materialized_views('effective_rent_%')")
            }
        }
    }

    @Test
    fun `could get daily historic asking rent`() {
        val result =
            runBlocking {
                target.getHistoricalRents(
                    ids = setOf("USFL-014695"),
                    idType = IdType.PROPERTY,
                    dateFrom = LocalDate.now().minusDays(10),
                    dateTo = LocalDate.now(),
                    periodicity = HistoricalPeriodicity.DAILY,
                    rentType = RentType.ASKING,
                    bedrooms = null,
                    bathrooms = null,
                    unitCondition = null,
                )
            }

        Assertions.assertEquals(11, result.size)
    }

    @Test
    fun `could get daily historic msa asking rent`() {
        val result =
            runBlocking {
                target.getHistoricalRents(
                    ids = setOf(msaCode),
                    idType = IdType.MSA,
                    dateFrom = LocalDate.now().minusDays(10),
                    dateTo = LocalDate.now(),
                    periodicity = HistoricalPeriodicity.DAILY,
                    rentType = RentType.ASKING,
                    bedrooms = null,
                    bathrooms = null,
                    unitCondition = null,
                )
            }

        Assertions.assertEquals(11, result.size)
    }

    @Test
    fun `could get daily historic zip asking rent`() {
        val result =
            runBlocking {
                target.getHistoricalRents(
                    ids = setOf(zipCode),
                    idType = IdType.ZIP_CODE,
                    dateFrom = LocalDate.now().minusDays(10),
                    dateTo = LocalDate.now(),
                    periodicity = HistoricalPeriodicity.DAILY,
                    rentType = RentType.ASKING,
                    bedrooms = null,
                    bathrooms = null,
                    unitCondition = null,
                )
            }

        Assertions.assertEquals(11, result.size)
    }

    @Test
    fun `could get daily historic msa effective rent`() {
        val result =
            runBlocking {
                target.getHistoricalRents(
                    ids = setOf(msaCode),
                    idType = IdType.MSA,
                    dateFrom = LocalDate.now().minusDays(10),
                    dateTo = LocalDate.now(),
                    periodicity = HistoricalPeriodicity.DAILY,
                    rentType = RentType.EFFECTIVE,
                    bedrooms = null,
                    bathrooms = null,
                    unitCondition = null,
                )
            }

        Assertions.assertEquals(11, result.size)
    }

    @Test
    fun `could get daily historic zip effective rent`() {
        val result =
            runBlocking {
                target.getHistoricalRents(
                    ids = setOf(zipCode),
                    idType = IdType.ZIP_CODE,
                    dateFrom = LocalDate.now().minusDays(10),
                    dateTo = LocalDate.now(),
                    periodicity = HistoricalPeriodicity.DAILY,
                    rentType = RentType.EFFECTIVE,
                    bedrooms = null,
                    bathrooms = null,
                    unitCondition = null,
                )
            }

        Assertions.assertEquals(11, result.size)
    }

    @Test
    fun `could get daily RENO historic asking rent`() {
        val propertyId = "USFL-014695"
        units
            .map {
                PropertyUnit(
                    propertyId = propertyId,
                    unitId = it,
                    squareFootage = BigDecimal.TEN,
                    bedrooms = 1,
                    bathrooms = BigDecimal.ONE,
                    floorPlan = "CC",
                    amenities = setOf("NONE"),
                    renovationProbability = BigDecimal(0.9),
                    renovated = true,
                )
            }.forEach(propUnitRepo::saveOrUpdate)

        val resultNON =
            runBlocking {
                target.getHistoricalRents(
                    ids = setOf(propertyId),
                    idType = IdType.PROPERTY,
                    dateFrom = LocalDate.now().minusDays(10),
                    dateTo = LocalDate.now(),
                    periodicity = HistoricalPeriodicity.DAILY,
                    rentType = RentType.ASKING,
                    bedrooms = null,
                    bathrooms = null,
                    unitCondition = UnitCondition.NON_RENO,
                )
            }

        Assertions.assertEquals(0, resultNON.size)

        val resultRENO =
            runBlocking {
                target.getHistoricalRents(
                    ids = setOf(propertyId),
                    idType = IdType.PROPERTY,
                    dateFrom = LocalDate.now().minusDays(10),
                    dateTo = LocalDate.now(),
                    periodicity = HistoricalPeriodicity.DAILY,
                    rentType = RentType.ASKING,
                    bedrooms = null,
                    bathrooms = null,
                    unitCondition = UnitCondition.RENO,
                )
            }

        Assertions.assertEquals(11, resultRENO.size)
    }

    @Test
    fun `could get daily NON_RENO historic asking rent`() {
        val propertyId = "USFL-014695"
        units
            .map {
                PropertyUnit(
                    propertyId = propertyId,
                    unitId = it,
                    squareFootage = BigDecimal.TEN,
                    bedrooms = 1,
                    bathrooms = BigDecimal.ONE,
                    floorPlan = "CC",
                    amenities = setOf("NONE"),
                    renovationProbability = BigDecimal(0.5),
                    renovated = false,
                )
            }.forEach(propUnitRepo::saveOrUpdate)

        val resultNON =
            runBlocking {
                target.getHistoricalRents(
                    ids = setOf(propertyId),
                    idType = IdType.PROPERTY,
                    dateFrom = LocalDate.now().minusDays(10),
                    dateTo = LocalDate.now(),
                    periodicity = HistoricalPeriodicity.DAILY,
                    rentType = RentType.ASKING,
                    bedrooms = null,
                    bathrooms = null,
                    unitCondition = UnitCondition.NON_RENO,
                )
            }

        Assertions.assertEquals(11, resultNON.size)

        val resultRENO =
            runBlocking {
                target.getHistoricalRents(
                    ids = setOf(propertyId),
                    idType = IdType.PROPERTY,
                    dateFrom = LocalDate.now().minusDays(10),
                    dateTo = LocalDate.now(),
                    periodicity = HistoricalPeriodicity.DAILY,
                    rentType = RentType.ASKING,
                    bedrooms = null,
                    bathrooms = null,
                    unitCondition = UnitCondition.RENO,
                )
            }

        Assertions.assertEquals(0, resultRENO.size)
    }

    @Test
    fun `could get monthly historic asking rent`() {
        val dateFrom = LocalDate.now().minusMonths(10)
        val dateTo = LocalDate.now()

        val result =
            runBlocking {
                target.getHistoricalRents(
                    ids = setOf("USFL-014695"),
                    idType = IdType.PROPERTY,
                    dateFrom = dateFrom,
                    dateTo = dateTo,
                    periodicity = HistoricalPeriodicity.MONTHLY,
                    rentType = RentType.ASKING,
                    bedrooms = null,
                    bathrooms = null,
                    unitCondition = null,
                )
            }

        Assertions.assertEquals(11, result.size)
    }

    @Test
    fun `could get weekly historic asking rent`() {
        val result =
            runBlocking {
                target.getHistoricalRents(
                    ids = setOf("USFL-014695"),
                    idType = IdType.PROPERTY,
                    dateFrom = LocalDate.now().minusWeeks(10),
                    dateTo = LocalDate.now(),
                    periodicity = HistoricalPeriodicity.WEEKLY,
                    rentType = RentType.ASKING,
                    bedrooms = null,
                    bathrooms = null,
                    unitCondition = null,
                )
            }

        Assertions.assertEquals(11, result.size)
    }

    @Test
    fun `could get daily historic effective rent`() {
        val result =
            runBlocking {
                target.getHistoricalRents(
                    ids = setOf("USFL-014695"),
                    idType = IdType.PROPERTY,
                    dateFrom = LocalDate.now().minusDays(10),
                    dateTo = LocalDate.now(),
                    periodicity = HistoricalPeriodicity.DAILY,
                    rentType = RentType.EFFECTIVE,
                    bedrooms = null,
                    bathrooms = null,
                    unitCondition = null,
                )
            }

        Assertions.assertEquals(11, result.size)
    }

    @Test
    fun `could get monthly historic effective rent`() {
        val monthts = 10
        val dateFrom = LocalDate.now().minusMonths(monthts.toLong())
        val dateTo = LocalDate.now()
        val difference = dateFrom.monthsUntil(dateTo)

        val result =
            runBlocking {
                target.getHistoricalRents(
                    ids = setOf("USFL-014695"),
                    idType = IdType.PROPERTY,
                    dateFrom = dateFrom,
                    dateTo = dateTo,
                    periodicity = HistoricalPeriodicity.MONTHLY,
                    rentType = RentType.EFFECTIVE,
                    bedrooms = null,
                    bathrooms = null,
                    unitCondition = null,
                )
            }

        Assertions.assertEquals(difference, result.size)
    }

    @Test
    fun `could get weekly historic effective rent`() {
        val weeks = 10
        val dateFrom = LocalDate.now().minusWeeks(weeks.toLong())
        val dateTo = LocalDate.now()
        val result =
            runBlocking {
                target.getHistoricalRents(
                    ids = setOf("USFL-014695"),
                    idType = IdType.PROPERTY,
                    dateFrom = dateFrom,
                    dateTo = dateTo,
                    periodicity = HistoricalPeriodicity.WEEKLY,
                    rentType = RentType.EFFECTIVE,
                    bedrooms = null,
                    bathrooms = null,
                    unitCondition = null,
                )
            }

        Assertions.assertEquals(11, result.size)
    }

    private fun getRentListing(
        rent: Money = Money.of("1000"),
        dateFrom: LocalDate = LocalDate.now(),
        dateTo: LocalDate = LocalDate.now(),
        bedroomsQuantity: Int = 2,
        bathroomsQuantity: BigDecimal = BigDecimal("1"),
    ) = RentListing(
        id = UUID.randomUUID().toString(),
        propertyId = "USFL-014695",
        type = RentListingType.UNIT,
        typeId = "9-305",
        rent = rent,
        dateFrom = dateFrom,
        dateTo = dateTo,
        recordSource = "apartments",
        zipCode = zipCode,
        msaCode = msaCode,
        unitSquareFootage = BigDecimal("1119"),
        bedroomsQuantity = bedroomsQuantity,
        bathroomsQuantity = bathroomsQuantity,
        floorPlan = "A2",
        availableIn = null,
        rentDeposit = Money.of("600"),
        createdAt = DateUtils.now(),
        updateAt = DateUtils.now(),
    )

    private fun LocalDate.monthsUntil(endDate: LocalDate): Int {
        val baseMonths = ChronoUnit.MONTHS.between(this, endDate)

        return when {
            baseMonths > 0 ->
                baseMonths.toInt() +
                    if (dayOfMonth != 1 || endDate.dayOfMonth != 1) 1 else 0

            else -> if (dayOfMonth != endDate.dayOfMonth) 1 else 0
        }
    }

    private fun LocalDate.weeksUntil(endDate: LocalDate): Int {
        val baseWeeks = ChronoUnit.WEEKS.between(this, endDate)

        return when {
            baseWeeks > 0 ->
                baseWeeks.toInt() +
                    if (dayOfWeek.value != 1 || endDate.dayOfWeek.value != 1) 1 else 0

            else -> if (dayOfWeek.value != endDate.dayOfWeek.value) 1 else 0
        }
    }
}
