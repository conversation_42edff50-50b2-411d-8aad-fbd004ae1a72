package utils

import com.keyway.core.entities.EffectiveRent
import com.keyway.core.entities.GeoPoint
import com.keyway.core.entities.Money
import com.keyway.core.entities.MultifamilyProperty
import com.keyway.core.entities.RentListing
import com.keyway.core.entities.RentListingType
import com.keyway.core.entities.config.UnitSimilarityCalculatorConfig
import com.keyway.core.utils.DateUtils
import java.math.BigDecimal
import java.time.LocalDate
import java.time.OffsetDateTime
import java.util.UUID

object MockedEntityFactory {
    fun buildRentListing(
        id: String = UUID.randomUUID().toString(),
        propertyId: String = "USTX-027626",
        type: RentListingType = RentListingType.UNIT,
        typeId: String = "104",
        rent: Money = Money.of(1091),
        dateFrom: LocalDate = LocalDate.parse("2023-10-17"),
        dateTo: LocalDate = LocalDate.parse("2023-10-17"),
        recordSource: String = "apartments",
        zipCode: String = "75217",
        msaCode: String = "19100",
        unitSquareFootage: BigDecimal? = BigDecimal("895"),
        bedroomsQuantity: Int = 2,
        bathroomsQuantity: BigDecimal? = BigDecimal("1.5"),
        floorPlan: String? = "A2",
        availableIn: LocalDate? = LocalDate.parse("2023-10-17"),
        rentDeposit: Money? = Money.zero(),
        createdAt: OffsetDateTime = DateUtils.now(),
        updateAt: OffsetDateTime = DateUtils.now(),
        isActive: Boolean = true,
    ): RentListing =
        RentListing(
            id = id,
            propertyId = propertyId,
            type = type,
            typeId = typeId,
            rent = rent,
            dateFrom = dateFrom,
            dateTo = dateTo,
            recordSource = recordSource,
            zipCode = zipCode,
            msaCode = msaCode,
            unitSquareFootage = unitSquareFootage,
            bedroomsQuantity = bedroomsQuantity,
            bathroomsQuantity = bathroomsQuantity,
            floorPlan = floorPlan,
            availableIn = availableIn,
            rentDeposit = rentDeposit,
            createdAt = createdAt,
            updateAt = updateAt,
            isActive = isActive,
        )

    fun buildEffectiveRent(
        id: String = UUID.randomUUID().toString(),
        listing: RentListing,
        concessionIds: List<String> = emptyList(),
        dateFrom: LocalDate = DateUtils.now(),
        dateTo: LocalDate = DateUtils.now(),
        rent: Money = Money.of("1725.00"),
        rentDeposit: Money? = Money.zero(),
        concessions: String = "",
        createdAt: OffsetDateTime = DateUtils.now(),
        updateAt: OffsetDateTime = DateUtils.now(),
        isActive: Boolean = true,
    ): EffectiveRent =
        EffectiveRent.build(
            id = id,
            listing = listing,
            concessionIds = concessionIds,
            dateFrom = dateFrom,
            dateTo = dateTo,
            rent = rent,
            rentDeposit = rentDeposit,
            concessions = concessions,
            createdAt = createdAt,
            updateAt = updateAt,
            isActive = isActive,
        )

    fun buildUnitSimilarityCalculatorConfig(
        organizationId: String = "DEFAULT",
        sqftWeight: Double = 0.5,
        bedroomWeight: Double = 0.3,
        bathroomWeight: Double = 0.15,
        amenitiesWeight: Double = 0.05,
        sqftSigma: Double = 75.0,
        bedroomSigma: Double = 0.75,
        bathroomSigma: Double = 0.5,
        amenitiesSigma: Double = 1.0,
        renoSimilarity: Double = 0.7,
        minAmountUnitsToCompare: Int = 5,
        similarityPercentageFilter: Double = 0.6,
    ) = UnitSimilarityCalculatorConfig(
        organizationId,
        sqftWeight,
        bedroomWeight,
        bathroomWeight,
        amenitiesWeight,
        sqftSigma,
        bedroomSigma,
        bathroomSigma,
        amenitiesSigma,
        renoSimilarity,
        minAmountUnitsToCompare,
        similarityPercentageFilter,
    )

    fun buildMultifamilyProperty(
        id: String = "TEST-${UUID.randomUUID()}",
        address: String = "123 Test Street",
        fullAddress: String = "123 Test Street, Test City, TX 12345",
        city: String = "Test City",
        county: String? = "Test County",
        zipCode: Long = 12345L,
        state: String = "TX",
        location: GeoPoint =
            GeoPoint(
                latitude = BigDecimal("32.7767"),
                longitude = BigDecimal("-96.7970"),
            ),
        geolocation: String = "POINT(-96.7970 32.7767)",
        squareFootage: BigDecimal? = BigDecimal("50000"),
        squareFootagePerUnit: BigDecimal? = BigDecimal("1000"),
        sourceType: String? = null, // SourceTypes? = null when defined
        tractCode: Long? = 48113001001L,
        constructionYear: Int? = 2020,
        renovationYear: Int? = 2022,
        unitQuantity: Int = 50,
        occupancyPercentage: BigDecimal? = BigDecimal("95.5"),
        propertyAmenities: Set<String> = setOf("Pool", "Gym", "Parking"),
        unitsAmenities: Set<String> = setOf("Balcony", "Air Conditioning"),
        isActive: Boolean = true,
        qualityOverallScore: BigDecimal? = BigDecimal("7.5"),
        lastSeen: LocalDate? = LocalDate.now(),
        stories: Int? = 5,
        propertyStyle: String? = "Modern",
        landSizeSqft: BigDecimal? = BigDecimal("75000"),
        housingSegment: Set<String> = setOf("Luxury", "Mid-Market"),
        hasAffordableUnits: Boolean? = false,
    ) = MultifamilyProperty(
        id = id,
        address = address,
        city = city,
        county = county,
        zipCode = zipCode,
        state = state,
        location = location,
        geolocation = geolocation,
        squareFootage = squareFootage,
        squareFootagePerUnit = squareFootagePerUnit,
        sourceType = sourceType,
        tractCode = tractCode,
        constructionYear = constructionYear,
        renovationYear = renovationYear,
        unitQuantity = unitQuantity,
        occupancyPercentage = occupancyPercentage,
        propertyAmenities = propertyAmenities,
        unitsAmenities = unitsAmenities,
        isActive = isActive,
        qualityOverallScore = qualityOverallScore,
        lastSeen = lastSeen,
        stories = stories,
        propertyStyle = propertyStyle,
        landSizeSqft = landSizeSqft,
        housingSegment = housingSegment,
        hasAffordableUnits = hasAffordableUnits,
    )
}
