import org.jetbrains.kotlin.gradle.dsl.JvmTarget
import org.jetbrains.kotlin.gradle.tasks.KotlinCompile

val koinVersion: String by rootProject
val junitVersion: String by rootProject
val unirestVersion: String by rootProject
val kommonsMapperVersion: String by rootProject
val ktorVersion: String by rootProject
val postgresVersion: String by rootProject
val flywayVersion: String by rootProject
val hikariVersion: String by rootProject
val kommonsSqsVersion: String by rootProject
val awsSdkVersion: String by rootProject
val kommonsAwsVersion: String by rootProject
val kommonsDbVersion: String by rootProject
val mockkVersion: String by rootProject
val kotestVersion: String by rootProject
val awsKotlinSdkVersion: String by rootProject

dependencies {
    testImplementation(project(":adapters"))
    testImplementation(project(":application"))
    testImplementation(project(":core"))

    testImplementation("io.ktor:ktor-server-call-logging:$ktorVersion")
    testImplementation("io.ktor:ktor-server-content-negotiation:$ktorVersion")
    testImplementation("io.ktor:ktor-server-core:$ktorVersion")
    testImplementation("io.ktor:ktor-server-netty:$ktorVersion")
    testImplementation("io.ktor:ktor-server-status-pages:$ktorVersion")
    testImplementation("io.ktor:ktor-server-cors:$ktorVersion")
    testImplementation("io.ktor:ktor-serialization-jackson:$ktorVersion") {
        exclude(group = "com.fasterxml.jackson.core")
        exclude(group = "com.fasterxml.jackson.module", module = "jackson-module-kotlin")
    }

    // Mapper
    testImplementation("com.keyway:kommons-mapper:$kommonsMapperVersion") {
        exclude(group = "com.google.code.gson")
    }

    // Rest
    testImplementation("com.konghq:unirest-objectmapper-jackson:$unirestVersion")
    testImplementation("com.konghq:unirest-java:$unirestVersion")

    // Test
    testImplementation("io.insert-koin:koin-test:$koinVersion")
    testImplementation("io.insert-koin:koin-test-junit5:$koinVersion")
    testImplementation(platform("org.junit:junit-bom:$junitVersion"))
    testImplementation("org.junit.jupiter:junit-jupiter")
    testRuntimeOnly("org.junit.platform:junit-platform-launcher")

    // Database
    implementation("com.keyway:kommons-db:$kommonsDbVersion")
    implementation("org.postgresql:postgresql:$postgresVersion")
    implementation("com.zaxxer:HikariCP:$hikariVersion")
    implementation("org.flywaydb:flyway-database-postgresql:$flywayVersion")

    // AWS
    implementation("com.keyway:kommons-aws:$kommonsAwsVersion")
    implementation(platform("software.amazon.awssdk:bom:$awsSdkVersion"))
    implementation("software.amazon.awssdk:sqs")
    implementation("software.amazon.awssdk:s3")

    implementation("aws.sdk.kotlin:cloudwatch:$awsKotlinSdkVersion")

    // SQS
    implementation("com.keyway:kommons-sqs:$kommonsSqsVersion")

    // mockk
    testImplementation("io.mockk:mockk:$mockkVersion")

    testImplementation("io.kotest:kotest-assertions-core:$kotestVersion")
}

tasks.withType<KotlinCompile> {
    compilerOptions {
        jvmTarget.set(JvmTarget.JVM_17)
    }
}
