#!/bin/sh

DATADOG_SETUP=""
if [ "$DATADOG_ENABLED" == "true" ]; then
  export DD_AGENT_HOST=$(curl --silent ${ECS_CONTAINER_METADATA_URI}/task | jq -r '.Containers[0].Networks[0].IPv4Addresses[0]')
  DATADOG_SETUP="-javaagent:/code/dd-java-agent.jar"
fi

# Set 80% of the total memory as the max heap size
MAX_HEAP_MEMORY="$(awk '/MemTotal/ {printf "%.0fm", ($2/1024)*0.80 == int(($2/1024)*0.80) ? ($2/1024)*0.80 : int(($2/1024)*0.80) + 1}' /proc/meminfo)"

java -Xmx${MAX_HEAP_MEMORY} ${DATADOG_SETUP} -jar /code/service.jar