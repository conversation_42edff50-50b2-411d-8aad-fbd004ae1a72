package com.keyway.adapters.handlers.rest.concessions

import com.keyway.adapters.converters.dto.ConcessionConverter
import com.keyway.adapters.dtos.concessions.ConcessionResponse
import com.keyway.adapters.dtos.concessions.PropertiesConcessionInput
import com.keyway.adapters.executor.UseCaseExecutor
import com.keyway.core.entities.concessions.PropertyConcessionV2
import com.keyway.core.usecases.concessions.GetPropertiesConcessionsV2UseCase

class GetPropertiesConcessionsHandler(
    private val useCaseExecutor: UseCaseExecutor,
    private val getPropertiesConcessionsV2UseCase: GetPropertiesConcessionsV2UseCase,
) {
    fun invoke(input: PropertiesConcessionInput): List<ConcessionResponse> =
        useCaseExecutor.invoke(
            useCase = getPropertiesConcessionsV2UseCase,
            inputDto = input,
            outputConverter = { buildResponse(it) },
            inputConverter = { ConcessionConverter.toGetPropertiesConcessionsInput(input) },
        )

    private fun buildResponse(result: List<PropertyConcessionV2>): List<ConcessionResponse> =
        result.map {
            ConcessionResponse(
                propertyId = it.propertyId,
                dateFrom = it.dateFrom,
                dateTo = it.dateTo,
                description = it.concessionText,
                active = it.active,
                benefits = emptyList(),
            )
        }
}
