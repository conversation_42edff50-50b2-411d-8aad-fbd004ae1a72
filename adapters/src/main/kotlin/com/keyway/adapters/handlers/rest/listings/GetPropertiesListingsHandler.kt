package com.keyway.adapters.handlers.rest.listings

import com.keyway.adapters.converters.dto.PropertiesListingConverter
import com.keyway.adapters.converters.dto.PropertiesListingConverter.toResponse
import com.keyway.adapters.dtos.listings.PropertiesListingDataInput
import com.keyway.adapters.dtos.listings.PropertiesListingsResponse
import com.keyway.adapters.executor.UseCaseExecutor
import com.keyway.core.dto.listings.output.RentListingWithEffectiveRentOutput
import com.keyway.core.usecases.listings.GetPropertiesListingsUseCase

class GetPropertiesListingsHandler(
    private val useCaseExecutor: UseCaseExecutor,
    private val getPropertiesListingsUseCase: GetPropertiesListingsUseCase,
) {
    fun invoke(input: PropertiesListingDataInput): List<PropertiesListingsResponse> =
        useCaseExecutor.invoke(
            useCase = getPropertiesListingsUseCase,
            inputDto = input,
            outputConverter = { buildResponse(it) },
            inputConverter = { PropertiesListingConverter.toGetPropertiesListingsInput(it) },
        )

    private fun buildResponse(output: Map<String, List<RentListingWithEffectiveRentOutput>>): List<PropertiesListingsResponse> =
        output.map { (propertyId, unitRentListings) ->
            PropertiesListingsResponse(
                propertyId = propertyId,
                listings =
                    unitRentListings.map { unitRentListing ->
                        toResponse(unitRentListing.rentListing, unitRentListing.effectiveRents)
                    },
            )
        }
}
