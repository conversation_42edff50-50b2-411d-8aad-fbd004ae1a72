package com.keyway.adapters.handlers.concessions

import com.keyway.adapters.executor.UseCaseExecutor
import com.keyway.adapters.mappers.SqsMapper
import com.keyway.core.dto.concessions.input.SaveConcessionV2Input
import com.keyway.core.usecases.concessions.SaveConcessionsV2UseCase
import com.keyway.kommons.mapper.dataclass.mapTo
import com.keyway.kommons.sqs.IMessageHandler
import org.slf4j.LoggerFactory
import software.amazon.awssdk.services.sqs.model.Message

class SaveConcessionsV2Handler(
    val useCaseExecutor: UseCaseExecutor,
    private val saveConcessionsUseCase: SaveConcessionsV2UseCase,
) : IMessageHandler {
    private val logger = LoggerFactory.getLogger(this::class.java)

    override fun processMessage(message: Message): Boolean =
        runCatching {
            useCaseExecutor(
                useCase = saveConcessionsUseCase,
                inputDto = SqsMapper.decode(message.body(), SaveConcessionV2Input::class.java),
                inputConverter = { it.mapTo() },
            )
        }.onFailure { error ->
            val decodedMessage =
                runCatching {
                    SqsMapper.decode(message.body(), SaveConcessionV2Input::class.java)
                }.getOrNull()

            logger.warn(
                """
                [CONCESSIONS_CONSUMER] Error during concession saving:
                |Message ID: {}
                |Property ID: {}
                |Error type: {}
                |Error cause: {}
                """.trimMargin(),
                message.messageId(),
                decodedMessage?.propertyId ?: "Unknown",
                error.javaClass.simpleName,
                error.cause,
            )
            return false
        }.isSuccess
}
