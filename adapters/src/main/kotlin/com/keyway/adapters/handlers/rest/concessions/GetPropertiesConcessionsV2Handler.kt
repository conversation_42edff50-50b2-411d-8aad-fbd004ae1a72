package com.keyway.adapters.handlers.rest.concessions

import com.keyway.adapters.dtos.concessions.Concession
import com.keyway.adapters.executor.UseCaseExecutor
import com.keyway.core.dto.concessions.input.GetPropertiesConcessionsV2Input
import com.keyway.core.entities.concessions.PropertyConcessionV2
import com.keyway.core.usecases.concessions.GetPropertiesConcessionsV2UseCase
import com.keyway.kommons.mapper.dataclass.mapTo

class GetPropertiesConcessionsV2Handler(
    private val useCaseExecutor: UseCaseExecutor,
    private val getPropertiesConcessionsV2UseCase: GetPropertiesConcessionsV2UseCase,
) {
    fun invoke(input: GetPropertiesConcessionsV2Input): List<Concession> =
        useCaseExecutor.invoke(
            useCase = getPropertiesConcessionsV2UseCase,
            inputDto = input,
            outputConverter = { buildResponse(it) },
            inputConverter = { input },
        )

    private fun buildResponse(result: List<PropertyConcessionV2>): List<Concession> =
        result.map {
            Concession(
                propertyId = it.propertyId,
                dateFrom = it.dateFrom,
                dateTo = it.dateTo,
                description = it.concessionText,
                active = it.active,
                benefits =
                    it.benefits.map { benefit ->
                        benefit.mapTo()
                    },
            )
        }
}
