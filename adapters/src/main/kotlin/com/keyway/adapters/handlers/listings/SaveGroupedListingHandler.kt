package com.keyway.adapters.handlers.listings

import com.keyway.adapters.converters.dto.SaveListingsConverter.toGroupedInput
import com.keyway.adapters.dtos.listings.GroupedRentListingMessage
import com.keyway.adapters.executor.UseCaseExecutor
import com.keyway.adapters.mappers.SqsMapper
import com.keyway.core.dto.listings.input.SaveRentGroupedListingInput
import com.keyway.core.usecases.UseCase
import com.keyway.core.usecases.listings.SaveGroupedListingUseCase
import com.keyway.core.usecases.listings.SaveHistoricalGroupedListingUseCase
import com.keyway.core.utils.RecordSourceHelper
import com.keyway.kommons.mapper.JsonMapper
import com.keyway.kommons.mapper.type.JacksonComplexType
import com.keyway.kommons.sqs.IMessageHandler
import org.slf4j.LoggerFactory
import software.amazon.awssdk.services.sqs.model.Message
import kotlin.reflect.full.isSubtypeOf
import kotlin.reflect.full.memberProperties
import kotlin.reflect.full.starProjectedType

class SaveGroupedListingHandler(
    private val useCaseExecutor: UseCaseExecutor,
    private val saveGroupedListingUseCase: SaveGroupedListingUseCase,
    private val saveHistoricalGroupedListingUseCase: SaveHistoricalGroupedListingUseCase,
) : IMessageHandler {
    private val logger = LoggerFactory.getLogger(this::class.java)

    companion object {
        const val IMPORT_PROCESS = "listing_data"
    }

    private val requiredFields =
        GroupedRentListingMessage::class.memberProperties.mapNotNull { prop ->
            prop.name
                .takeUnless {
                    prop.returnType.isMarkedNullable ||
                        prop.returnType.isSubtypeOf(List::class.starProjectedType)
                }?.replace("(?<=.)[A-Z]".toRegex(), "_$0")
                ?.lowercase()
        }

    override fun processMessage(message: Message): Boolean =
        runCatching {
            message.parseMessage()?.let {
                useCaseExecutor(
                    useCase = useCase(it),
                    inputDto = it,
                    inputConverter = ::toGroupedInput,
                )
            } ?: message.logMissingData()
        }.onFailure {
            logger.warn("[LISTING_CONSUMER] Error during grouping event consumer - import_process:$IMPORT_PROCESS", it)
            return false
        }.isSuccess

    private fun useCase(message: GroupedRentListingMessage): UseCase<SaveRentGroupedListingInput, Unit> =
        saveGroupedListingUseCase.takeUnless { message.isHistorical() }
            ?: saveHistoricalGroupedListingUseCase

    private fun GroupedRentListingMessage.isHistorical(): Boolean =
        this.records.any {
            RecordSourceHelper.historicSources.contains(it.recordSource)
        }

    private fun Message.parseMessage(): GroupedRentListingMessage? = runCatching { SqsMapper.decode(this.body(), GroupedRentListingMessage::class.java) }.getOrNull()

    private fun Message.logMissingData() =
        SqsMapper.decode(this.body(), object : JacksonComplexType<Map<String, Any?>>() {}).let { incomingMessage ->
            requiredFields
                .associateWith { field ->
                    incomingMessage.getOrDefault(field, null)
                }.let { incomingData ->
                    logger.warn(
                        """[INCOMING_DATA_ERROR]
                    Missing fields: ${JsonMapper.encode(
                            requiredFields.filter {
                                incomingData.getOrDefault(it, null) == null
                            },
                        )} 
                    Incoming Data: ${JsonMapper.encode(incomingData)} 
                    import_process:$IMPORT_PROCESS
                        """.trimMargin(),
                    )
                }
        }
}
