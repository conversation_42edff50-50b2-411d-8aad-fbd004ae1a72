package com.keyway.adapters.handlers.suggestion

import com.keyway.adapters.dtos.listings.UnitComparison
import com.keyway.adapters.dtos.listings.UnitRentSuggestion
import com.keyway.adapters.dtos.listings.UnitRentSuggestions
import com.keyway.adapters.executor.UseCaseExecutor
import com.keyway.core.dto.listings.input.CalculateRentSuggestionInput
import com.keyway.core.dto.listings.output.CalculateRentSuggestionOutput
import com.keyway.core.usecases.listings.CalculateRentSuggestionUseCase
import java.time.LocalDate

class GetRentSuggestionHandler(
    private val useCaseExecutor: UseCaseExecutor,
    private val calculateRentSuggestionUseCase: CalculateRentSuggestionUseCase,
) {
    operator fun invoke(input: Input): UnitRentSuggestions =
        useCaseExecutor(
            useCase = calculateRentSuggestionUseCase,
            inputDto = input,
            outputConverter = { it.toResponse() },
            inputConverter = { it.toUseCaseInput() },
        )

    private fun Input.toUseCaseInput() =
        CalculateRentSuggestionInput(
            organizationId = this.organizationId,
            basePropertyId = this.basePropertyId,
            propertyIds = this.propertiesIds,
            dateFrom = this.dateFrom,
            dateTo = this.dateTo,
        )

    data class Input(
        val organizationId: String?,
        val basePropertyId: String,
        val propertiesIds: Set<String>,
        val dateFrom: LocalDate?,
        val dateTo: LocalDate?,
    )

    private fun CalculateRentSuggestionOutput.toResponse() =
        UnitRentSuggestions(
            propertyId = this.propertyId,
            unitRentSuggestions =
                this.units.map { unit ->
                    UnitRentSuggestion(
                        unitId = unit.unitId,
                        askingRent = unit.askingRent,
                        effectiveRent = unit.effectiveRent,
                        similarUnitComparisons =
                            unit.similarUnitsComparisons.map { comparison ->
                                UnitComparison(
                                    propertyId = comparison.propertyId,
                                    unitId = comparison.unitId,
                                    similarityScore = comparison.similarityScore,
                                )
                            },
                    )
                },
        )
}
