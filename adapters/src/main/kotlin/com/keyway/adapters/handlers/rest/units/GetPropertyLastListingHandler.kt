package com.keyway.adapters.handlers.rest.units

import com.keyway.adapters.converters.dto.PropertiesListingConverter.toResponse
import com.keyway.adapters.dtos.listings.RentListingInput
import com.keyway.adapters.dtos.listings.RentListingsResponse
import com.keyway.adapters.executor.UseCaseExecutor
import com.keyway.core.dto.listings.output.RentListingWithEffectiveRentOutput
import com.keyway.core.usecases.listings.GetPropertyLastListingUseCase
import com.keyway.kommons.mapper.dataclass.mapTo

class GetPropertyLastListingHandler(
    private val useCaseExecutor: UseCaseExecutor,
    private val getPropertyLastListingUseCase: GetPropertyLastListingUseCase,
) {
    fun invoke(input: RentListingInput): RentListingsResponse =
        useCaseExecutor.invoke(
            useCase = getPropertyLastListingUseCase,
            inputDto = input,
            outputConverter = { buildResponse(input.propertyId, it) },
            inputConverter = { it.mapTo() },
        )

    private fun buildResponse(
        propertyId: String,
        unitRentData: List<RentListingWithEffectiveRentOutput>,
    ): RentListingsResponse =
        RentListingsResponse(
            propertyId = propertyId,
            units =
                unitRentData.map {
                    toResponse(it.rentListing, it.effectiveRents)
                },
        )
}
