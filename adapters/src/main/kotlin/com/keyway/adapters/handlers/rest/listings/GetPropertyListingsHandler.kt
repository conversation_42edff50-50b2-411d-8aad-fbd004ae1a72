package com.keyway.adapters.handlers.rest.listings

import com.keyway.adapters.converters.dto.PropertiesListingConverter.toResponse
import com.keyway.adapters.dtos.listings.ListingDataInput
import com.keyway.adapters.dtos.listings.PropertiesListingsResponse
import com.keyway.adapters.executor.UseCaseExecutor
import com.keyway.core.dto.listings.output.RentListingWithEffectiveRentOutput
import com.keyway.core.usecases.listings.GetPropertyListingsUseCase
import com.keyway.kommons.mapper.dataclass.mapTo

class GetPropertyListingsHandler(
    private val useCaseExecutor: UseCaseExecutor,
    private val getListingsUseCase: GetPropertyListingsUseCase,
) {
    fun invoke(input: ListingDataInput): PropertiesListingsResponse =
        useCaseExecutor.invoke(
            useCase = getListingsUseCase,
            inputDto = input,
            outputConverter = { buildResponse(input.propertyId, it) },
            inputConverter = { it.mapTo() },
        )

    private fun buildResponse(
        propertyId: String,
        unitRentData: List<RentListingWithEffectiveRentOutput>,
    ): PropertiesListingsResponse =
        PropertiesListingsResponse(
            propertyId = propertyId,
            listings =
                unitRentData.map {
                    toResponse(it.rentListing, it.effectiveRents)
                },
        )
}
