package com.keyway.adapters.handlers.historical

import com.keyway.adapters.dtos.metrics.historical.HistoricalRentSummary
import com.keyway.adapters.dtos.metrics.historical.HistoricalSummary
import com.keyway.adapters.dtos.metrics.historical.MSAHistoricalRentSummary
import com.keyway.adapters.dtos.metrics.historical.ZipHistoricalRentSummary
import com.keyway.adapters.executor.UseCaseExecutor
import com.keyway.core.dto.historical.HistoricalRentSummaryDto
import com.keyway.core.dto.query.metrics.IdType
import com.keyway.core.entities.HistoricalPeriodicity
import com.keyway.core.entities.RentType
import com.keyway.core.entities.UnitCondition
import com.keyway.core.usecases.historical.GetHistoricalRents
import com.keyway.core.usecases.historical.GetHistoricalRents.Input
import com.keyway.kommons.mapper.dataclass.mapTo
import java.math.BigDecimal
import java.time.LocalDate
import java.time.temporal.ChronoUnit

class GetHistoricalRentsByIds(
    private val useCaseExecutor: UseCaseExecutor,
    private val getPropertiesHistoricalRents: GetHistoricalRents,
) {
    companion object {
        const val MAX_DAILY_AMOUNT = 45L
        const val MAX_WEEKLY_AMOUNT = 30L
        const val MAX_MONTHLY_AMOUNT = 30L
    }

    suspend operator fun invoke(
        ids: Set<String>,
        idType: IdType,
        dateFrom: LocalDate,
        dateTo: LocalDate,
        rentType: RentType,
        periodicity: HistoricalPeriodicity?,
        bedrooms: Int?,
        bathrooms: BigDecimal?,
        unitCondition: UnitCondition?,
    ): List<HistoricalSummary> =
        Input(
            ids = ids,
            idType = idType,
            dateFrom = dateFrom,
            dateTo = dateTo,
            periodicity = periodicity ?: calculatePeriodicity(dateFrom, dateTo),
            rentType = rentType,
            bedrooms = bedrooms,
            bathrooms = bathrooms,
            unitCondition = unitCondition,
        ).let(::fixToMaxRange)
            .let { input ->
                useCaseExecutor(
                    useCase = getPropertiesHistoricalRents,
                    inputDto = input,
                    inputConverter = { it },
                    outputConverter = { output ->
                        output.map { idType.mapResponse(it) }
                    },
                )
            }

    private fun IdType.mapResponse(output: HistoricalRentSummaryDto): HistoricalSummary =
        when (this) {
            IdType.PROPERTY ->
                output.mapTo<HistoricalRentSummaryDto, HistoricalRentSummary>(
                    additions = mapOf("propertyId" to output.id),
                )
            IdType.ZIP_CODE ->
                output.mapTo<HistoricalRentSummaryDto, ZipHistoricalRentSummary>(
                    additions = mapOf("zipCode" to output.id),
                )
            IdType.MSA ->
                output.mapTo<HistoricalRentSummaryDto, MSAHistoricalRentSummary>(
                    additions = mapOf("msaCode" to output.id),
                )
        }

    private fun calculatePeriodicity(
        dateFrom: LocalDate,
        dateTo: LocalDate,
    ): HistoricalPeriodicity =
        (dateFrom.until(dateTo, ChronoUnit.DAYS)).let {
            when {
                it > MAX_WEEKLY_AMOUNT * 7 -> HistoricalPeriodicity.MONTHLY
                it > MAX_DAILY_AMOUNT -> HistoricalPeriodicity.WEEKLY
                else -> HistoricalPeriodicity.DAILY
            }
        }

    private fun fixToMaxRange(input: Input): Input =
        when (input.periodicity) {
            HistoricalPeriodicity.DAILY ->
                input.dateTo.minusDays(MAX_DAILY_AMOUNT)
            HistoricalPeriodicity.WEEKLY ->
                input.dateTo.minusWeeks(MAX_WEEKLY_AMOUNT)
            HistoricalPeriodicity.MONTHLY ->
                input.dateTo.minusMonths(MAX_MONTHLY_AMOUNT)
        }.takeIf { input.dateFrom <= it }
            ?.let { input.copy(dateFrom = it) }
            ?: input
}
