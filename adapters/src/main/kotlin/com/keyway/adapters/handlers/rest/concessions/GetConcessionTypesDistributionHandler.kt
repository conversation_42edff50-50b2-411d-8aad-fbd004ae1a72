package com.keyway.adapters.handlers.rest.concessions

import com.keyway.adapters.executor.UseCaseExecutor
import com.keyway.core.dto.query.metrics.IdType
import com.keyway.core.entities.concessions.ConcessionTypesDistribution
import com.keyway.core.usecases.concessions.GetConcessionTypesDistributionUseCase
import com.keyway.core.utils.DateUtils
import java.time.LocalDate

class GetConcessionTypesDistributionHandler(
    private val useCaseExecutor: UseCaseExecutor,
    private val getConcessionTypesDistributionUseCase: GetConcessionTypesDistributionUseCase,
) {
    operator fun invoke(
        idType: IdType,
        id: String,
        dateFrom: LocalDate?,
        dateTo: LocalDate?,
    ): ConcessionTypesDistribution =
        useCaseExecutor.invoke(
            useCase = getConcessionTypesDistributionUseCase,
            inputDto =
                GetConcessionTypesDistributionUseCase.Input(
                    idType,
                    id,
                    dateFrom ?: DateUtils.getDefaultDateFrom(),
                    dateTo ?: DateUtils.getDefaultDateTo(),
                ),
            outputConverter = { it },
            inputConverter = { it },
        )
}
