package com.keyway.adapters.handlers.rest.metrics

import com.keyway.adapters.dtos.metrics.geo.MSABedroomRentSummary
import com.keyway.adapters.dtos.metrics.geo.MSARentSummary
import com.keyway.adapters.dtos.metrics.geo.ZipBedroomRentSummary
import com.keyway.adapters.dtos.metrics.geo.ZipRentSummary
import com.keyway.adapters.executor.UseCaseExecutor
import com.keyway.core.dto.listings.input.AggregatedMetricType
import com.keyway.core.dto.listings.input.ComputeAggregatedGeoMetricInput
import com.keyway.core.dto.listings.output.GeoRentMetricOutput
import com.keyway.core.dto.query.metrics.IdType
import com.keyway.core.usecases.metrics.ComputeAggregatedGeoMetricUseCase
import com.keyway.kommons.mapper.dataclass.mapTo
import java.time.LocalDate

class GetGeoMetricsHandler(
    private val useCaseExecutor: UseCaseExecutor,
    private val computeAggregatedMetricUseCase: ComputeAggregatedGeoMetricUseCase,
) {
    suspend operator fun invoke(input: Input): List<Any> =
        useCaseExecutor.invoke(
            useCase = computeAggregatedMetricUseCase,
            inputDto = input,
            outputConverter = { buildResponse(it, input.idType, input.metricType) },
            inputConverter = { buildInput(it) },
        )

    private fun buildInput(it: Input): ComputeAggregatedGeoMetricInput =
        ComputeAggregatedGeoMetricInput(
            ids = it.ids,
            idType = it.idType,
            dateFrom = it.dateFrom,
            dateTo = it.dateTo,
            type = it.metricType,
        )

    private fun buildResponse(
        result: List<GeoRentMetricOutput>,
        idType: IdType,
        metricType: AggregatedMetricType,
    ): List<Any> =
        when {
            idType == IdType.MSA && metricType == AggregatedMetricType.BEDROOMS ->
                result.map {
                    it.mapTo<GeoRentMetricOutput, MSABedroomRentSummary>(
                        additions = mapOf("msaCode" to it.id),
                    )
                }
            idType == IdType.MSA && metricType == AggregatedMetricType.BY_ID ->
                result.map {
                    it.mapTo<GeoRentMetricOutput, MSARentSummary>(
                        additions = mapOf("msaCode" to it.id),
                    )
                }
            idType == IdType.ZIP_CODE && metricType == AggregatedMetricType.BEDROOMS ->
                result.map {
                    it.mapTo<GeoRentMetricOutput, ZipBedroomRentSummary>(
                        additions = mapOf("zipCode" to it.id),
                    )
                }
            idType == IdType.ZIP_CODE && metricType == AggregatedMetricType.BY_ID ->
                result.map {
                    it.mapTo<GeoRentMetricOutput, ZipRentSummary>(
                        additions = mapOf("zipCode" to it.id),
                    )
                }
            else -> emptyList()
        }

    data class Input(
        val ids: Set<String>,
        val idType: IdType,
        val dateFrom: LocalDate?,
        val dateTo: LocalDate?,
        val metricType: AggregatedMetricType,
    )
}
