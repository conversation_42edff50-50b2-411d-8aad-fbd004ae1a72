package com.keyway.adapters.handlers.multifamilly

import com.keyway.adapters.converters.dto.multifamily.MultifamilyPropertyMessageConverter.toMultifamilyProperty
import com.keyway.adapters.dtos.multifamily.MultifamilyPropertyMessage
import com.keyway.core.usecases.multifamily.SaveOrUpdateProperty
import com.keyway.kommons.mapper.JsonMapper
import com.keyway.kommons.sqs.IMessageHandler
import org.slf4j.LoggerFactory
import software.amazon.awssdk.services.sqs.model.Message

class SavePropertyData(
    private val saveOrUpdateProperty: SaveOrUpdateProperty,
) : IMessageHandler {
    private val logger = LoggerFactory.getLogger(this::class.java)

    companion object {
        const val IMPORT_PROCESS = "property_data"
    }

    override fun processMessage(message: Message): Boolean =
        runCatching {
            message
                .body()
                .let {
                    JsonMapper.decode(it, MultifamilyPropertyMessage::class.java)
                }.toMultifamilyProperty()
                .let(saveOrUpdateProperty::execute)
            true
        }.getOrElse { error ->
            logger.error("""[SAVE_PROPERTY_CONSUMER] Error on save property data - import_process:$IMPORT_PROCESS""", error)
            false
        }
}
