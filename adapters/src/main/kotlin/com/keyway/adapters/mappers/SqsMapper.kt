package com.keyway.adapters.mappers

import com.fasterxml.jackson.databind.module.SimpleModule
import com.keyway.kommons.mapper.Mapper
import com.keyway.kommons.mapper.jackson.JacksonMapper
import com.keyway.kommons.mapper.jackson.useDefaultJsonConfig
import com.keyway.kommons.mapper.type.ComplexType

// TODO: MOVE IT TO kommons-mapper
object SqsMapper : Mapper {
    private lateinit var mapper: Mapper

    override fun encode(obj: Any): String = getMapper().encode(obj)

    override fun <T> decode(
        str: String,
        clazz: Class<T>,
    ): T = getMapper().decode(str, clazz)

    override fun <T> decode(
        str: String,
        complexType: ComplexType<T>,
    ): T = getMapper().decode(str, complexType)

    private fun getMapperConfig() =
        useDefaultJsonConfig().registerModules(
            SimpleModule(),
        )

    private fun getMapper(): Mapper {
        if (!this::mapper.isInitialized) {
            mapper = JacksonMapper(getMapperConfig())
        }
        return mapper
    }
}
