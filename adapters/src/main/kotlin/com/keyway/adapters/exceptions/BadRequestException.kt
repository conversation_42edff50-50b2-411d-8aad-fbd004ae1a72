package com.keyway.adapters.exceptions

import org.apache.hc.core5.http.HttpStatus
import org.apache.hc.core5.http.impl.EnglishReasonPhraseCatalog
import java.util.Locale

open class BadRequestException(
    message: String = EnglishReasonPhraseCatalog.INSTANCE.getReason(HttpStatus.SC_BAD_REQUEST, Locale.ENGLISH),
    httpStatusCode: Int = HttpStatus.SC_BAD_REQUEST,
    errorCode: String = "BAD_REQUEST",
    cause: Throwable? = null,
) : RestException(
        message = message,
        httpStatusCode = httpStatusCode,
        errorCode = errorCode,
        statusCode = HttpStatus.SC_BAD_REQUEST,
        cause = cause,
    ) {
    override fun getLoggerLevel() = ExceptionLogLevel.WARN
}
