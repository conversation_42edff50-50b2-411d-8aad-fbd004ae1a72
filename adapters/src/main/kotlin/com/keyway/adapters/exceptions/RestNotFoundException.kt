package com.keyway.adapters.exceptions

import org.apache.hc.core5.http.HttpStatus
import org.apache.hc.core5.http.impl.EnglishReasonPhraseCatalog
import java.util.Locale

open class RestNotFoundException(
    message: String = EnglishReasonPhraseCatalog.INSTANCE.getReason(HttpStatus.SC_NOT_FOUND, Locale.ENGLISH),
    httpStatusCode: Int = HttpStatus.SC_NOT_FOUND,
    errorCode: String = "NOT_FOUND",
    cause: Throwable? = null,
) : RestException(
        message = message,
        httpStatusCode = httpStatusCode,
        errorCode = errorCode,
        statusCode = HttpStatus.SC_NOT_FOUND,
        cause = cause,
    ) {
    override fun getLoggerLevel() = ExceptionLogLevel.WARN
}
