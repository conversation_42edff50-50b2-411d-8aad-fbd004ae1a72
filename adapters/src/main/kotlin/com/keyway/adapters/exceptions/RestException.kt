package com.keyway.adapters.exceptions

import com.keyway.adapters.dtos.error.response.RestExceptionResponse

enum class ExceptionLogLevel {
    INFO,
    WARN,
    ERROR,
}

abstract class RestException(
    val httpStatusCode: Int,
    val statusCode: Int,
    val errorCode: String,
    message: String,
    cause: Throwable? = null,
) : RuntimeException("$httpStatusCode | $errorCode - $message", cause) {
    fun getResponse(): RestExceptionResponse =
        RestExceptionResponse(
            message = this.message ?: "Unexpected error",
            httpStatusCode = this.httpStatusCode,
            statusCode = this.statusCode,
            errorCode = this.errorCode,
        )

    open fun getLoggerLevel(): ExceptionLogLevel = ExceptionLogLevel.ERROR
}
