package com.keyway.adapters.converters.dto.utils

import com.keyway.adapters.handlers.utils.HandlersUtils.MAX_PROPERTIES_ALLOWED
import com.keyway.adapters.handlers.utils.HandlersUtils.MIN_PROPERTIES_ALLOWED
import com.keyway.adapters.handlers.utils.HandlersUtils.PROPERTIES_DELIMITER

object ConverterUtils {
    fun getValidPropertySet(propIdList: Set<String>): Set<String> {
        val result =
            propIdList
                .map { it.trim().split(PROPERTIES_DELIMITER) }
                .flatten()
                .filter { it.isNotBlank() }

        require(result.size <= MAX_PROPERTIES_ALLOWED) { "The maximum amount of properties allowed is $MAX_PROPERTIES_ALLOWED" }
        require(result.size >= MIN_PROPERTIES_ALLOWED) { "The minimum amount of properties allowed is $MIN_PROPERTIES_ALLOWED" }

        return result.toSet()
    }
}
