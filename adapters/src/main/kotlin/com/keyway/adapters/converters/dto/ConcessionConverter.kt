package com.keyway.adapters.converters.dto

import com.keyway.adapters.converters.dto.utils.ConverterUtils.getValidPropertySet
import com.keyway.adapters.dtos.concessions.PropertiesConcessionInput
import com.keyway.core.dto.concessions.input.GetPropertiesConcessionsV2Input

object ConcessionConverter {
    fun toGetPropertiesConcessionsInput(input: PropertiesConcessionInput): GetPropertiesConcessionsV2Input =
        GetPropertiesConcessionsV2Input(
            propertyIds = getValidPropertySet(input.propertiesIds),
            dateFrom = input.dateFrom,
            dateTo = input.dateTo,
            isActive = input.active,
        )
}
