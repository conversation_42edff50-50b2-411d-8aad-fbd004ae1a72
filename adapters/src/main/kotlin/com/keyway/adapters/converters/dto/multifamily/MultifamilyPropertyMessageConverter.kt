package com.keyway.adapters.converters.dto.multifamily

import com.keyway.adapters.dtos.multifamily.MultifamilyPropertyMessage
import com.keyway.core.entities.MultifamilyProperty
import java.math.BigDecimal
import java.math.RoundingMode

object MultifamilyPropertyMessageConverter {
    fun MultifamilyPropertyMessage.toMultifamilyProperty(): MultifamilyProperty {
        val realEstateData = this.additionalData?.realEstateData
        val unitQuantity = realEstateData?.units?.quantity ?: realEstateData?.unitMix?.quantity ?: 1

        return MultifamilyProperty(
            id = this.id,
            address = this.address,
            city = this.city,
            county = this.county,
            zipCode = this.zipCode,
            state = this.state,
            location = this.location,
            geolocation = "POINT(${this.location.longitude} ${this.location.latitude})",
            squareFootage = this.squareFootage,
            squareFootagePerUnit = calculateSquareFootagePerUnit(this.squareFootage, unitQuantity),
            sourceType = this.sourceType,
            tractCode = this.tractCode,
            constructionYear = this.constructionYear,
            renovationYear = this.renovationYear,
            unitQuantity = unitQuantity,
            occupancyPercentage = realEstateData?.occupancyPercentage,
            propertyAmenities = realEstateData?.propertyAmenities ?: emptySet(),
            unitsAmenities = realEstateData?.unitsAmenities ?: emptySet(),
            isActive = this.active,
            qualityOverallScore = null,
            lastSeen = null,
            stories = realEstateData?.stories,
            propertyStyle = realEstateData?.propertyStyle,
            landSizeSqft = realEstateData?.landSizeSqft,
            housingSegment = realEstateData?.housingSegment?.toSet() ?: emptySet(),
            hasAffordableUnits = realEstateData?.hasAffordableUnits,
        )
    }

    private fun calculateSquareFootagePerUnit(
        squareFootage: BigDecimal?,
        unitQuantity: Int,
    ): BigDecimal? =
        if (squareFootage != null && unitQuantity > 0) {
            squareFootage.divide(BigDecimal(unitQuantity), 2, RoundingMode.HALF_UP)
        } else {
            null
        }
}
