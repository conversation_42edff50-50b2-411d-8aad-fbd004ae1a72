package com.keyway.adapters.converters.exception

import com.keyway.adapters.exceptions.BadRequestException
import com.keyway.adapters.exceptions.InternalServerException
import com.keyway.adapters.exceptions.RestException
import com.keyway.adapters.exceptions.RestNotFoundException
import com.keyway.core.exceptions.base.NotFoundException
import com.keyway.core.exceptions.base.UnexpectedException
import com.keyway.core.exceptions.base.UseCaseException

object ExceptionsConverter {
    operator fun invoke(error: Throwable): RestException =
        when (error) {
            is UseCaseException, is IllegalArgumentException,
            ->
                BadRequestException(
                    message = error.localizedMessage,
                    cause = error,
                )
            is NotFoundException ->
                RestNotFoundException(
                    message = error.localizedMessage,
                    cause = error,
                )
            is UnexpectedException ->
                InternalServerException(
                    message = error.localizedMessage,
                    cause = error,
                )
            else ->
                InternalServerException(
                    message = error.localizedMessage,
                    cause = error,
                )
        }
}
