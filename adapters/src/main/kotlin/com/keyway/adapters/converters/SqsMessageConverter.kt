package com.keyway.adapters.converters

import com.fasterxml.jackson.databind.PropertyNamingStrategies
import com.fasterxml.jackson.databind.annotation.JsonNaming
import com.keyway.adapters.mappers.SqsMapper
import com.keyway.kommons.mapper.JsonMapper
import software.amazon.awssdk.services.sqs.model.Message

object SqsMessageConverter {
    @JsonNaming(PropertyNamingStrategies.UpperCamelCaseStrategy::class)
    data class SqsMessageWrapper(
        val message: String,
        val messageAttributes: Map<String, Map<String, String>> = emptyMap(),
    ) {
        fun getAttribute(attribute: String): String =
            this.messageAttributes[attribute]?.get("Value")
                ?: throw Exception("Missing Message Attribute $attribute")

        inline fun <reified T> toMessageBody(): T = SqsMapper.decode(this.message, T::class.java)
    }

    inline fun <reified T> toMessageBody(message: Message): T =
        JsonMapper
            .decode(
                message.body(),
                SqsMessageWrapper::class.java,
            ).let { messageBody ->
                SqsMapper.decode(messageBody.message, T::class.java)
            }
}
