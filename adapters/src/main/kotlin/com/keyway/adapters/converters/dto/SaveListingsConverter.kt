package com.keyway.adapters.converters.dto

import com.keyway.adapters.dtos.listings.GroupedRentListingMessage
import com.keyway.adapters.exceptions.InvalidSaveRentListingMessageException
import com.keyway.core.dto.listings.input.SaveRentGroupedListingInput
import com.keyway.core.dto.listings.input.UnitRecordsData
import com.keyway.core.entities.RentListingType

object SaveListingsConverter {
    fun toGroupedInput(message: GroupedRentListingMessage): SaveRentGroupedListingInput =
        SaveRentGroupedListingInput(
            propertyId = message.propertyId,
            type = message.type,
            typeId = getTypeId(message),
            zipCode = message.zipCode,
            msaCode = message.msaCode,
            unitSquareFootage = message.unitSquareFootage,
            bedroomsQuantity = message.bedroomsQuantity,
            bathroomsQuantity = message.bathroomsQuantity,
            floorPlan = message.floorPlan,
            records =
                message.records.map {
                    UnitRecordsData(
                        recordSource = it.recordSource,
                        rent = it.rent,
                        rentDeposit = it.rentDeposit,
                        effectiveRent = it.effectiveRent,
                        effectiveRentDeposit = it.effectiveRentDeposit,
                        availableIn = it.availableIn,
                        recordDate = it.recordDate,
                        concessions = it.concessionText ?: "",
                    )
                },
        )

    // TODO: MAKE AN ABSTRACTION OF THE BASE CLASS
    private fun getTypeId(message: GroupedRentListingMessage): String =
        if (message.type == RentListingType.FLOOR_PLAN) {
            message.floorPlan
                ?: throw InvalidSaveRentListingMessageException(
                    "FLOOR_PLAN Listings must contain floor_plan",
                )
        } else {
            message.unitId
        }
}
