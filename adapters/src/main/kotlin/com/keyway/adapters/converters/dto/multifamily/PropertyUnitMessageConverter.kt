package com.keyway.adapters.converters.dto.multifamily

import com.keyway.adapters.dtos.units.PropertyUnitMessage
import com.keyway.core.entities.property.PropertyUnit
import com.keyway.kommons.mapper.dataclass.mapTo

object PropertyUnitMessageConverter {
    fun PropertyUnitMessage.toPropertyUnits(): List<PropertyUnit> =
        this.units.map {
            it.mapTo(
                fieldMappings = mapOf("id" to "unitId"),
                additions =
                    mapOf(
                        "propertyId" to this.propertyId,
                    ),
            )
        }
}
