package com.keyway.adapters.repositories.utils

import java.math.BigDecimal
import java.math.RoundingMode

object PostgresQueryUtils {
    fun Map<String, Any>.getBigDecimal(
        key: String,
        decimalScale: Int = 2,
    ): BigDecimal? = (this[key] as BigDecimal?)?.setScale(decimalScale, RoundingMode.HALF_UP)

    fun Map<String, Any>.getLong(key: String): Long = this[key] as Long

    fun Map<String, Any>.getInt(key: String): Int = (this[key] as Long).toInt()

    fun Map<String, Any>.getOptionalInt(key: String): Int? = (this[key] as Int?)

    fun Map<String, Any>.getDouble(key: String): Double? = this[key] as Double?

    fun Map<String, Any>.getString(key: String): String = this[key] as String
}
