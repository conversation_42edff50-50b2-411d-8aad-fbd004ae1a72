package com.keyway.adapters.repositories.utils

import com.keyway.core.entities.RentListingType
import com.keyway.core.entities.UnitCondition

object UnitConditionUtils {
    fun UnitCondition.getUnitConditionJoin(
        propUnitAlias: String = "pu",
        rentAlias: String = "rl",
    ): String =
        """ LEFT JOIN  property_units $propUnitAlias 
            ON $propUnitAlias.property_id = $rentAlias.property_id 
           AND $propUnitAlias.unit_id = $rentAlias.type_id 
           AND $rentAlias.type = '${RentListingType.UNIT.name}' """

    fun UnitCondition.getWhere(propUnitAlias: String = "pu"): String =
        when (this) {
            UnitCondition.RENO -> " AND $propUnitAlias.renovated = true "
            UnitCondition.NON_RENO -> " AND ($propUnitAlias.renovated IS NULL OR $propUnitAlias.renovated = false) "
        }
}
