package com.keyway.adapters.repositories

import com.keyway.adapters.exceptions.InternalServerException
import com.keyway.core.entities.config.UnitSimilarityCalculatorConfig
import com.keyway.core.ports.repositories.UnitSimilarityCalculatorConfigRepository
import com.keyway.kommons.db.SqlClient

class PostgresUnitSimilarityCalculatorConfigRepository(
    private val sqlClient: SqlClient,
) : UnitSimilarityCalculatorConfigRepository {
    companion object {
        const val DEFAULT_CONFIG_ORG_ID = "DEFAULT"
    }

    override fun get(organizationId: String): UnitSimilarityCalculatorConfig? {
        val query =
            """
            select *
            from unit_similarity_calculator_config
            where organization_id = ?
            """.trimIndent()

        return sqlClient.get(
            query = query,
            params = listOf(organizationId),
            clazz = UnitSimilarityCalculatorConfig::class.java,
        )
    }

    override fun getDefault(): UnitSimilarityCalculatorConfig =
        get(DEFAULT_CONFIG_ORG_ID)
            ?: throw InternalServerException("DEFAULT UNIT SIMILARITY CALCULATOR CONFIG NOT FOUND")
}
