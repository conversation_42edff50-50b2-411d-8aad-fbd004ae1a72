package com.keyway.adapters.repositories

import com.keyway.adapters.repositories.model.EffectiveRentDBModel
import com.keyway.adapters.repositories.utils.RentUtils
import com.keyway.core.entities.EffectiveRent
import com.keyway.core.ports.repositories.EffectiveRentRepository
import com.keyway.kommons.db.SqlClient
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import java.time.LocalDate

class PostgresEffectiveRentRepository(
    private val sqlClient: SqlClient,
) : EffectiveRentRepository {
    override fun save(effectiveRent: EffectiveRent) {
        this.save(listOf(effectiveRent))
    }

    override fun save(effectiveRents: List<EffectiveRent>) {
        if (effectiveRents.isEmpty()) return

        val valuesSql =
            effectiveRents.joinToString(", ") {
                "(?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)"
            }

        val fullQuery =
            """
            INSERT INTO effective_rent (
                id,
                rent_listing_id,
                concession_ids,
                date_from,
                date_to,
                rent,
                rent_deposit,
                concessions,
                created_at,
                update_at,
                is_active,
                property_id,
                type,
                type_id,                
                record_source,
                zip_code,
                msa_code,
                unit_square_footage,
                bedrooms,
                bathrooms,
                floor_plan
            ) VALUES 
            $valuesSql
            """.trimIndent()

        sqlClient.update(
            query = fullQuery,
            preparedStatementHandler = { ps ->
                RentUtils.effectivePreparedStatementHandler(effectiveRents, ps)
            },
        )
    }

    override fun update(effectiveRents: List<EffectiveRent>) {
        if (effectiveRents.isEmpty()) return

        val updateQuery =
            """
            UPDATE effective_rent AS er
            SET 
                rent_listing_id = v.rent_listing_id,
                concession_ids = v.concession_ids,
                date_from = v.date_from,
                date_to = v.date_to,
                rent = v.rent,
                rent_deposit = v.rent_deposit,
                concessions = v.concessions,
                created_at = v.created_at,
                update_at = v.update_at,
                is_active = v.is_active,
                property_id = v.property_id,
                type = v.type,
                type_id = v.type_id,                
                record_source = v.record_source,
                zip_code = v.zip_code,
                msa_code = v.msa_code,
                unit_square_footage = v.unit_square_footage,
                bedrooms = v.bedrooms,
                bathrooms = v.bathrooms,
                floor_plan = v.floor_plan
            FROM (
                VALUES ${effectiveRents.joinToString(", ") { "(?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)" }}
            ) AS v (
                id, rent_listing_id, concession_ids, date_from, date_to, rent, rent_deposit, concessions,
                created_at, update_at, is_active,
                property_id, type, type_id, record_source,
                zip_code, msa_code, unit_square_footage, bedrooms, bathrooms, floor_plan
            )
            WHERE er.id = v.id
            """.trimIndent()

        sqlClient.update(
            query = updateQuery,
            preparedStatementHandler = { ps ->
                RentUtils.effectivePreparedStatementHandler(effectiveRents, ps)
            },
        )
    }

    override fun getById(effectiveRentId: String): EffectiveRent =
        sqlClient
            .getOneOrFail(
                """SELECT * FROM effective_rent where id = ?""",
                params = listOf(effectiveRentId),
                clazz = EffectiveRentDBModel::class.java,
                messageKey = "EFFECTIVE RENT NOT FOUND",
            ).toEffectiveRent()

    override fun getByListingId(listingId: String): List<EffectiveRent> =
        sqlClient
            .getAll(
                """SELECT * FROM effective_rent where rent_listing_id = ? 
                ${RentUtils.isActiveSql()}
                """.trimMargin(),
                params = listOf(listingId),
                clazz = EffectiveRentDBModel::class.java,
            ).map(EffectiveRentDBModel::toEffectiveRent)

    override suspend fun getByListingIds(
        listingIds: List<String>,
        dateFrom: LocalDate?,
        dateTo: LocalDate?,
        isActiveOnly: Boolean,
    ): List<EffectiveRent> =
        withContext(Dispatchers.IO) {
            if (listingIds.isEmpty()) return@withContext emptyList()
            sqlClient
                .getAll(
                    """
                    SELECT * 
                    FROM effective_rent 
                    WHERE rent_listing_id IN (${listingIds.joinToString(",") { "?" }}) 
                    ${RentUtils.isActiveSql().takeIf { isActiveOnly } ?: ""}
                    ${dateTo?.let { " AND date_from <= ?" } ?: ""}
                    ${dateFrom?.let { " AND date_to >= ?" } ?: ""}                
                    """.trimIndent(),
                    params = listingIds.toList().plus(listOfNotNull(dateTo, dateFrom)),
                    clazz = EffectiveRentDBModel::class.java,
                ).map(EffectiveRentDBModel::toEffectiveRent)
        }

    override fun deleteById(ids: List<String>) {
        val idsPlaceholders = ids.joinToString(",") { "?" }
        sqlClient.update(
            query = """DELETE FROM effective_rent WHERE id IN ($idsPlaceholders)""",
            params = ids,
        )
    }

    override fun deleteByListingId(ids: List<String>) {
        val listingsIdsPlaceholders = ids.joinToString(",") { "?" }
        sqlClient.update(
            query = """DELETE FROM effective_rent WHERE rent_listing_id IN ($listingsIdsPlaceholders)""",
            params = ids,
        )
    }

    override fun deleteInactive(limit: Int): Int {
        if (limit == 0) return 0

        val batchSize = 1000
        var remainingLimit = limit
        var totalDeleted = 0
        while (remainingLimit > 0) {
            val currentBatchSize = minOf(batchSize, remainingLimit)

            val deletedCount =
                sqlClient.update(
                    query =
                        """
                        WITH rows_to_delete AS (
                            SELECT id
                            FROM effective_rent
                            WHERE is_active = false
                            LIMIT ?
                        )
                        DELETE FROM effective_rent
                        WHERE id IN (SELECT id FROM rows_to_delete)
                        """.trimIndent(),
                    params = listOf(currentBatchSize),
                )

            // If no rows were deleted, break to avoid infinite loop
            if (deletedCount == 0) {
                break
            }
            totalDeleted += deletedCount
            remainingLimit -= deletedCount
        }
        return totalDeleted
    }
}
