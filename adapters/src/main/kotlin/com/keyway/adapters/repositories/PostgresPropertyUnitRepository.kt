package com.keyway.adapters.repositories

import com.fasterxml.jackson.databind.JsonNode
import com.keyway.core.entities.property.PropertyUnit
import com.keyway.core.ports.repositories.PropertyUnitRepository
import com.keyway.core.utils.DateUtils
import com.keyway.kommons.db.SqlClient
import com.keyway.kommons.db.mapper.DatabaseMapper
import com.keyway.kommons.mapper.type.JacksonComplexType
import org.postgresql.util.PGobject
import setNullableBoolean
import java.math.BigDecimal
import java.time.OffsetDateTime

class PostgresPropertyUnitRepository(
    private val sqlClient: SqlClient,
) : PropertyUnitRepository {
    override fun saveOrUpdate(propertyUnit: PropertyUnit) {
        val now = DateUtils.now<OffsetDateTime>()
        val sql =
            """
            INSERT INTO property_units (
                property_id, unit_id, square_footage, bedrooms, bathrooms, 
                floor_plan, amenities, renovation_probability, renovated,
                created_at, updated_at
            ) 
            VALUES (
                ?, ?, ?, ?, ?, 
                ?, ?, ?, ?, ?, ?
            )
            ON CONFLICT (property_id, unit_id) DO UPDATE 
            SET 
                square_footage = EXCLUDED.square_footage,
                bedrooms = EXCLUDED.bedrooms,
                bathrooms = EXCLUDED.bathrooms,
                floor_plan = EXCLUDED.floor_plan,
                amenities = EXCLUDED.amenities,
                renovation_probability = EXCLUDED.renovation_probability,
                renovated = EXCLUDED.renovated,
                updated_at = EXCLUDED.updated_at;
            """.trimIndent()
        var parameterIndex = 1
        sqlClient.update(
            sql,
        ) { ps ->
            ps.setString(parameterIndex++, propertyUnit.propertyId)
            ps.setString(parameterIndex++, propertyUnit.unitId)
            ps.setBigDecimal(parameterIndex++, propertyUnit.squareFootage)
            ps.setInt(parameterIndex++, propertyUnit.bedrooms)
            ps.setBigDecimal(parameterIndex++, propertyUnit.bathrooms)
            ps.setObject(parameterIndex++, propertyUnit.floorPlan)
            ps.setObject(
                parameterIndex++,
                PGobject().apply {
                    this.type = "jsonb"
                    this.value = DatabaseMapper.encode(propertyUnit.amenities)
                },
            )
            ps.setBigDecimal(parameterIndex++, propertyUnit.renovationProbability)
            ps.setNullableBoolean(parameterIndex++, propertyUnit.renovated)
            ps.setObject(parameterIndex++, now)
            ps.setObject(parameterIndex++, now)
        }
    }

    override fun findByPropertyId(propertyId: String): List<PropertyUnit> = findByProperties(setOf(propertyId))

    override fun findByProperties(propertiesIds: Set<String>): List<PropertyUnit> {
        if (propertiesIds.isEmpty()) return emptyList()
        val propertiesIdsPlaceholders = propertiesIds.joinToString(",") { "?" }

        val sql =
            """
            SELECT *
            FROM property_units 
            WHERE property_id IN ($propertiesIdsPlaceholders)
            """.trimIndent()

        return sqlClient
            .getAll(
                sql,
                propertiesIds.toList(),
                clazz = PropertyUnitDbModel::class.java,
            ).map { it.toPropertyUnit() }
    }

    override fun findByPropertyIdAndUnitId(
        propertyId: String,
        unitId: String,
    ): PropertyUnit? {
        val sql =
            """
            SELECT 
                property_id, unit_id, square_footage, bedrooms, bathrooms,
                floor_plan, amenities, renovation_probability, renovated
            FROM property_units 
            WHERE property_id = ? AND unit_id = ?
            """.trimIndent()

        return sqlClient.get(sql, listOf(propertyId, unitId), clazz = PropertyUnitDbModel::class.java)?.toPropertyUnit()
    }

    data class PropertyUnitDbModel(
        val unitId: String,
        val propertyId: String,
        val squareFootage: BigDecimal?,
        val bedrooms: Int,
        val bathrooms: BigDecimal?,
        val floorPlan: String?,
        val amenities: JsonNode,
        val renovationProbability: BigDecimal?,
        val renovated: Boolean?,
    ) {
        fun toPropertyUnit() =
            PropertyUnit(
                unitId = this.unitId,
                propertyId = this.propertyId,
                squareFootage = this.squareFootage,
                bedrooms = this.bedrooms,
                bathrooms = this.bathrooms,
                floorPlan = this.floorPlan,
                amenities =
                    DatabaseMapper.decode(
                        this.amenities.toString(),
                        object : JacksonComplexType<Set<String>>() {},
                    ),
                renovationProbability = this.renovationProbability,
                // IS INFO NOT PROVIDED, UNIT IS CONSIDERED NON_RENO
                renovated = this.renovated ?: false,
            )
    }
}
