package com.keyway.adapters.repositories

import com.keyway.adapters.repositories.utils.IdTypeUtils.getSqlColumn
import com.keyway.adapters.repositories.utils.toPGObject
import com.keyway.core.dto.query.concessions.ConcessionsQuery
import com.keyway.core.dto.query.metrics.IdType
import com.keyway.core.entities.concessions.ConcessionTypesDistribution
import com.keyway.core.entities.concessions.PropertyConcessionBenefit
import com.keyway.core.entities.concessions.PropertyConcessionV2
import com.keyway.core.ports.repositories.PropertyConcessionV2Repository
import com.keyway.kommons.db.SqlClient
import com.keyway.kommons.db.mapper.DatabaseMapper
import com.keyway.kommons.mapper.type.JacksonComplexType
import java.time.LocalDate
import java.time.OffsetDateTime
import kotlin.collections.map

class PostgresPropertyConcessionV2Repository(
    private val sqlClient: SqlClient,
) : PropertyConcessionV2Repository {
    companion object {
        private const val CONCESSIONS_V2_TABLE = "concessions_v2"
        private const val SELECT_FIELDS = "id, property_id, concession_text, benefits::TEXT, date_from, date_to, created_at, updated_at, zip_code, msa_code"
    }

    override fun saveOrUpdate(propertyConcession: PropertyConcessionV2) {
        sqlClient.update(
            """
            INSERT INTO $CONCESSIONS_V2_TABLE (
                id,
                property_id,
                concession_text,
                date_from,
                date_to,
                benefits,
                created_at,
                updated_at,
                zip_code,
                msa_code
            ) VALUES (?,?,?,?,?,?::jsonb,?,?,?,?)
            ON CONFLICT (id) DO UPDATE SET
                property_id = EXCLUDED.property_id,
                concession_text = EXCLUDED.concession_text,
                date_from = EXCLUDED.date_from,
                date_to = EXCLUDED.date_to,
                benefits = EXCLUDED.benefits::jsonb,
                updated_at = EXCLUDED.updated_at,
                zip_code = EXCLUDED.zip_code,
                msa_code = EXCLUDED.msa_code;
            """.trimIndent(),
        ) { ps ->
            ps.setString(1, propertyConcession.id)
            ps.setString(2, propertyConcession.propertyId)
            ps.setString(3, propertyConcession.concessionText)
            ps.setObject(4, propertyConcession.dateFrom)
            ps.setObject(5, propertyConcession.dateTo)
            ps.setObject(6, propertyConcession.benefits.toPGObject())
            ps.setObject(7, propertyConcession.createdAt)
            ps.setObject(8, propertyConcession.updatedAt)
            ps.setString(9, propertyConcession.zipCode)
            ps.setString(10, propertyConcession.msaCode)
        }
    }

    override fun findByPropertyId(
        propertyId: String,
        dateFrom: LocalDate,
        dateTo: LocalDate,
    ): List<PropertyConcessionV2> =
        sqlClient
            .getAll(
                query =
                    """
                    SELECT $SELECT_FIELDS
                    FROM $CONCESSIONS_V2_TABLE
                    WHERE property_id = ?
                    AND date_from <= ? AND date_to >= ?
                    ORDER BY date_from DESC
                    """.trimIndent(),
                params = listOfNotNull(propertyId, dateTo, dateFrom),
                clazz = ConcessionV2DBModel::class.java,
            ).map { it.toPropertyConcessionV2() }

    override fun findMostRecentConcessionByPropertyId(
        propertyId: String,
        dateFrom: LocalDate,
    ): PropertyConcessionV2? =
        sqlClient
            .get(
                query =
                    """
                    SELECT $SELECT_FIELDS
                    FROM $CONCESSIONS_V2_TABLE
                    WHERE property_id = ?
                    AND date_from <= ?
                    ORDER BY date_from DESC
                    LIMIT 1
                    """.trimIndent(),
                params = listOfNotNull(propertyId, dateFrom),
                clazz = ConcessionV2DBModel::class.java,
            )?.toPropertyConcessionV2()

    override fun findByPropertiesIds(query: ConcessionsQuery): List<PropertyConcessionV2> {
        val propertyIdsPlaceholders = query.propertyIds.joinToString(",") { "?" }
        return sqlClient
            .getAll(
                query =
                    """
                    SELECT $SELECT_FIELDS
                    FROM $CONCESSIONS_V2_TABLE
                    WHERE property_id IN ($propertyIdsPlaceholders)
                    AND date_from <= ?
                    AND date_to >= ?
                    ORDER BY property_id, date_from DESC
                    """.trimIndent(),
                params = query.propertyIds.toList().plus(listOfNotNull(query.dateTo, query.dateFrom)),
                clazz = ConcessionV2DBModel::class.java,
            ).map { it.toPropertyConcessionV2() }
    }

    override fun findActiveConcessionsByPropertiesIds(query: ConcessionsQuery): List<PropertyConcessionV2> {
        val propertyIdsPlaceholders = query.propertyIds.joinToString(",") { "?" }
        return sqlClient
            .getAll(
                query =
                    """
                    SELECT $SELECT_FIELDS
                    FROM $CONCESSIONS_V2_TABLE
                    WHERE property_id IN ($propertyIdsPlaceholders)
                    AND ? BETWEEN date_from AND date_to
                    ORDER BY property_id, date_from DESC
                    """.trimIndent(),
                params = query.propertyIds.toList().plus(listOfNotNull(query.dateTo)),
                clazz = ConcessionV2DBModel::class.java,
            ).map { it.toPropertyConcessionV2() }
    }

    override fun delete(ids: List<String>) {
        val concessionIdsPlaceholders = ids.joinToString(",") { "?" }
        sqlClient.update(
            query = """ DELETE FROM $CONCESSIONS_V2_TABLE WHERE id IN ($concessionIdsPlaceholders)""",
            params = ids,
        )
    }

    data class ConcessionV2DBModel(
        val id: String,
        val propertyId: String,
        val concessionText: String,
        val benefits: String?,
        val dateFrom: LocalDate,
        val dateTo: LocalDate,
        val createdAt: OffsetDateTime,
        val updatedAt: OffsetDateTime,
        val zipCode: String?,
        val msaCode: String?,
    ) {
        fun toPropertyConcessionV2(): PropertyConcessionV2 =
            PropertyConcessionV2(
                id = id,
                propertyId = propertyId,
                dateFrom = dateFrom,
                dateTo = dateTo,
                concessionText = concessionText,
                benefits =
                    this.benefits?.let { benefitsJson ->
                        DatabaseMapper.decode(
                            benefitsJson,
                            object : JacksonComplexType<List<PropertyConcessionBenefit>>() {},
                        )
                    } ?: emptyList(),
                createdAt = createdAt,
                updatedAt = updatedAt,
                zipCode = zipCode,
                msaCode = msaCode,
            )
    }

    override fun getConcessionTypes(
        idType: IdType,
        id: String,
        dateFrom: LocalDate,
        dateTo: LocalDate,
    ): ConcessionTypesDistribution {
        val column = idType.getSqlColumn()
        return sqlClient.get(
            query =
                """
                WITH extracted_data_expanded AS (
                    SELECT
                        $column,
                        jsonb_array_elements(benefits) as incentive_data
                    FROM concessions_v2
                    WHERE $column = '$id'
                    AND date_from <= ?
                    AND date_to >= ?
                ),
                aggregated_data AS (
                    SELECT
                        $column,
                        CASE
                            WHEN (incentive_data->>'free_months_amount')::numeric > 0 
                            OR (incentive_data->>'free_months_until')::numeric > 0
                            THEN 1
                            ELSE 0
                        END as free_rent,
                
                        CASE
                            WHEN (incentive_data->>'one_time_dollars_off_amount')::numeric > 0 
                            OR (incentive_data->>'one_time_dollars_off_percentage')::numeric > 0
                            THEN 1
                            ELSE 0
                        END as one_time_discount,
                
                        CASE
                            WHEN (incentive_data->>'recurring_dollars_off_amount')::numeric > 0 
                            OR (incentive_data->>'recurring_dollars_off_percentage')::numeric > 0
                            OR (incentive_data->>'recurring_months_term')::numeric > 0
                            THEN 1
                            ELSE 0
                        END as recurring_discount,
                
                        CASE
                            WHEN (incentive_data->>'cheaper_rent')::boolean = true
                                 OR (incentive_data->>'cheaper_move_in_fee')::boolean = true
                                 OR (incentive_data->>'cheaper_application_fee')::boolean = true
                                 OR (incentive_data->>'cheaper_security_deposit')::boolean = true
                                 OR (incentive_data->>'cheaper_administrative_fee')::boolean = true
                            THEN 1
                            ELSE 0
                        END as cheaper_fees,
                
                        CASE
                            WHEN (incentive_data->>'waived_administrative_fee')::boolean = true
                                 OR (incentive_data->>'waived_security_deposit')::boolean = true
                                 OR (incentive_data->>'waived_application_fee')::boolean = true
                                 OR (incentive_data->>'waived_move_in_fee')::boolean = true
                            THEN 1
                            ELSE 0
                        END as waived_fees
                    FROM extracted_data_expanded
                )
                SELECT
                    $column,
                    SUM(free_rent)::int as free_rent,
                    SUM(one_time_discount)::int as one_time_discount,
                    SUM(recurring_discount)::int as recurring_discount,
                    SUM(cheaper_fees)::int as cheaper_fees,
                    SUM(waived_fees)::int as waived_fees,
                    COUNT($column)::int as total
                FROM aggregated_data
                GROUP BY $column
                ORDER BY $column
                """.trimIndent(),
            params = listOf(dateTo, dateFrom),
            clazz = ConcessionTypesDistribution::class.java,
        ) ?: ConcessionTypesDistribution(
            freeRent = 0,
            oneTimeDiscount = 0,
            recurringDiscount = 0,
            cheaperFees = 0,
            waivedFees = 0,
            total = 0,
        )
    }
}
