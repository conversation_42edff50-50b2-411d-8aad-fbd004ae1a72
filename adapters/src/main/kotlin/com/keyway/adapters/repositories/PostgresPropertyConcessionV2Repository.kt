package com.keyway.adapters.repositories

import com.keyway.adapters.repositories.utils.toPGObject
import com.keyway.core.dto.query.concessions.ConcessionsQuery
import com.keyway.core.entities.concessions.PropertyConcessionBenefit
import com.keyway.core.entities.concessions.PropertyConcessionV2
import com.keyway.core.ports.repositories.PropertyConcessionV2Repository
import com.keyway.kommons.db.SqlClient
import com.keyway.kommons.db.mapper.DatabaseMapper
import com.keyway.kommons.mapper.type.JacksonComplexType
import java.time.LocalDate
import java.time.OffsetDateTime
import kotlin.collections.map

class PostgresPropertyConcessionV2Repository(
    private val sqlClient: SqlClient,
) : PropertyConcessionV2Repository {
    companion object {
        private const val CONCESSIONS_V2_TABLE = "concessions_v2"
        private const val SELECT_FIELDS = "id, property_id, concession_text, benefits::TEXT, date_from, date_to, created_at, updated_at, zip_code, msa_code"
    }

    override fun saveOrUpdate(propertyConcession: PropertyConcessionV2) {
        sqlClient.update(
            """
            INSERT INTO $CONCESSIONS_V2_TABLE (
                id,
                property_id,
                concession_text,
                date_from,
                date_to,
                benefits,
                created_at,
                updated_at,
                zip_code,
                msa_code
            ) VALUES (?,?,?,?,?,?::jsonb,?,?,?,?)
            ON CONFLICT (id) DO UPDATE SET
                property_id = EXCLUDED.property_id,
                concession_text = EXCLUDED.concession_text,
                date_from = EXCLUDED.date_from,
                date_to = EXCLUDED.date_to,
                benefits = EXCLUDED.benefits::jsonb,
                updated_at = EXCLUDED.updated_at,
                zip_code = EXCLUDED.zip_code,
                msa_code = EXCLUDED.msa_code;
            """.trimIndent(),
        ) { ps ->
            ps.setString(1, propertyConcession.id)
            ps.setString(2, propertyConcession.propertyId)
            ps.setString(3, propertyConcession.concessionText)
            ps.setObject(4, propertyConcession.dateFrom)
            ps.setObject(5, propertyConcession.dateTo)
            ps.setObject(6, propertyConcession.benefits.toPGObject())
            ps.setObject(7, propertyConcession.createdAt)
            ps.setObject(8, propertyConcession.updatedAt)
            ps.setString(9, propertyConcession.zipCode)
            ps.setString(10, propertyConcession.msaCode)
        }
    }

    override fun findByPropertyId(
        propertyId: String,
        dateFrom: LocalDate,
        dateTo: LocalDate,
    ): List<PropertyConcessionV2> =
        sqlClient
            .getAll(
                query =
                    """
                    SELECT $SELECT_FIELDS
                    FROM $CONCESSIONS_V2_TABLE
                    WHERE property_id = ?
                    AND date_from <= ? AND date_to >= ?
                    ORDER BY date_from DESC
                    """.trimIndent(),
                params = listOfNotNull(propertyId, dateTo, dateFrom),
                clazz = ConcessionV2DBModel::class.java,
            ).map { it.toPropertyConcessionV2() }

    override fun findMostRecentConcessionByPropertyId(
        propertyId: String,
        dateFrom: LocalDate,
    ): PropertyConcessionV2? =
        sqlClient
            .get(
                query =
                    """
                    SELECT $SELECT_FIELDS
                    FROM $CONCESSIONS_V2_TABLE
                    WHERE property_id = ?
                    AND date_from <= ?
                    ORDER BY date_from DESC
                    LIMIT 1
                    """.trimIndent(),
                params = listOfNotNull(propertyId, dateFrom),
                clazz = ConcessionV2DBModel::class.java,
            )?.toPropertyConcessionV2()

    override fun findByPropertiesIds(query: ConcessionsQuery): List<PropertyConcessionV2> {
        val propertyIdsPlaceholders = query.propertyIds.joinToString(",") { "?" }
        return sqlClient
            .getAll(
                query =
                    """
                    SELECT $SELECT_FIELDS
                    FROM $CONCESSIONS_V2_TABLE
                    WHERE property_id IN ($propertyIdsPlaceholders)
                    AND date_from <= ?
                    AND date_to >= ?
                    ORDER BY property_id, date_from DESC
                    """.trimIndent(),
                params = query.propertyIds.toList().plus(listOfNotNull(query.dateTo, query.dateFrom)),
                clazz = ConcessionV2DBModel::class.java,
            ).map { it.toPropertyConcessionV2() }
    }

    override fun findActiveConcessionsByPropertiesIds(query: ConcessionsQuery): List<PropertyConcessionV2> {
        val propertyIdsPlaceholders = query.propertyIds.joinToString(",") { "?" }
        return sqlClient
            .getAll(
                query =
                    """
                    SELECT $SELECT_FIELDS
                    FROM $CONCESSIONS_V2_TABLE
                    WHERE property_id IN ($propertyIdsPlaceholders)
                    AND ? BETWEEN date_from AND date_to
                    ORDER BY property_id, date_from DESC
                    """.trimIndent(),
                params = query.propertyIds.toList().plus(listOfNotNull(query.dateTo)),
                clazz = ConcessionV2DBModel::class.java,
            ).map { it.toPropertyConcessionV2() }
    }

    override fun delete(ids: List<String>) {
        val concessionIdsPlaceholders = ids.joinToString(",") { "?" }
        sqlClient.update(
            query = """ DELETE FROM $CONCESSIONS_V2_TABLE WHERE id IN ($concessionIdsPlaceholders)""",
            params = ids,
        )
    }

    data class ConcessionV2DBModel(
        val id: String,
        val propertyId: String,
        val concessionText: String,
        val benefits: String?,
        val dateFrom: LocalDate,
        val dateTo: LocalDate,
        val createdAt: OffsetDateTime,
        val updatedAt: OffsetDateTime,
        val zipCode: String?,
        val msaCode: String?,
    ) {
        fun toPropertyConcessionV2(): PropertyConcessionV2 =
            PropertyConcessionV2(
                id = id,
                propertyId = propertyId,
                dateFrom = dateFrom,
                dateTo = dateTo,
                concessionText = concessionText,
                benefits =
                    this.benefits?.let { benefitsJson ->
                        DatabaseMapper.decode(
                            benefitsJson,
                            object : JacksonComplexType<List<PropertyConcessionBenefit>>() {},
                        )
                    } ?: emptyList(),
                createdAt = createdAt,
                updatedAt = updatedAt,
                zipCode = zipCode,
                msaCode = msaCode,
            )
    }
}
