////package com.keyway.adapters.repositories
////
////import com.keyway.adapters.repositories.utils.IdTypeUtils.getSqlColumn
////import com.keyway.adapters.repositories.utils.PostgresQueryUtils.getBigDecimal
////import com.keyway.adapters.repositories.utils.PostgresQueryUtils.getInt
////import com.keyway.adapters.repositories.utils.PostgresQueryUtils.getOptionalInt
////import com.keyway.adapters.repositories.utils.PostgresQueryUtils.getString
////import com.keyway.adapters.repositories.utils.RentUtils
////import com.keyway.adapters.repositories.utils.UnitConditionUtils.getUnitConditionJoin
////import com.keyway.adapters.repositories.utils.UnitConditionUtils.getWhere
////import com.keyway.core.dto.AskingMetricDto
////import com.keyway.core.dto.EffectiveMetricDto
////import com.keyway.core.dto.Metric
////import com.keyway.core.dto.MetricDto
////import com.keyway.core.dto.query.metrics.MetricsFiltersQuery
////import com.keyway.core.entities.RentListingType
////import com.keyway.core.entities.metric.MetricType
////import com.keyway.core.ports.repositories.MetricsRepository
////import com.keyway.core.utils.ListingUtils
////import com.keyway.kommons.db.SqlClient
////import kotlinx.coroutines.Dispatchers
////import kotlinx.coroutines.async
////import kotlinx.coroutines.withContext
////import java.math.BigDecimal
////import java.time.LocalDate
////
////class PostgresMetricsV2Repository(
////    private val sqlClient: SqlClient,
////) : MetricsRepository {
////    companion object {
////        val groupersMap: Map<MetricType, List<String>> =
////            mapOf(
////                MetricType.BY_ID to emptyList(),
////                MetricType.BEDROOMS to listOf("bedrooms"),
////                MetricType.UNIT_MIX to listOf("bedrooms", "bathrooms"),
////                MetricType.FLOOR_PLAN to listOf("floor_plan", "bedrooms", "bathrooms"),
////                MetricType.UNITS to listOf("type_id", "floor_plan", "bedrooms", "bathrooms", "unit_square_footage"),
////            )
////    }
////
////    override suspend fun aggregateMetrics(metricsFiltersQuery: MetricsFiltersQuery): List<MetricDto> =
////        withContext(Dispatchers.IO) {
////            val propertyIdsPlaceholders = metricsFiltersQuery.ids.joinToString(",") { "?" }
////            val idColumn = metricsFiltersQuery.idType.getSqlColumn()
////            val groupers = listOf(idColumn).plus(groupersMap.getValue(metricsFiltersQuery.type))
////
////            val askingDeferred = async { getAskingMetrics(groupers, idColumn, propertyIdsPlaceholders, metricsFiltersQuery) }
////            val effectiveDeferred = async { getEffectiveMetrics(groupers, idColumn, propertyIdsPlaceholders, metricsFiltersQuery) }
////            val askingResult = askingDeferred.await()
////            val effectiveResult = effectiveDeferred.await()
////
////            val effectiveMap = effectiveResult.associateBy { it.code }
////            askingResult.map { ask ->
////                MetricDto(ask, effectiveMap[ask.code])
////            }
////        }
////
////
////
//    private fun getAskingMetrics(
//        groupers: List<String>,
//        idColumn: String,
//        propertyIdsPlaceholders: String,
//        metricsFiltersQuery: MetricsFiltersQuery,
//    ): List<AskingMetricDto> {
//
//        val viewsToQuery = identifyRentListingViewsRange(metricsFiltersQuery.dateFrom, metricsFiltersQuery.dateFrom)
//
//
//        val idsSqlString = metricsFiltersQuery.ids.joinToString(", ") { "'$it'" }
//        val query = viewsToQuery.joinToString(" UNION ALL") {"SELECT * from $it where " + metricsFiltersQuery.ids +"in ("+ idsSqlString+")" +
//                "AND date_of_record BETWEEN "+ metricsFiltersQuery.dateFrom.toString() + " AND " + metricsFiltersQuery.dateTo.toString()}
//
//        val query2=
//            "select min(min_sft) as min_sft," +
//                "      max(max_sft) as max_sft," +
//                "      round(sum(sft_sum) / sum(units),2) as avg_ssft," +
//                "      min(min_rent)   as min_rent," +
//                "      max(max_rent) as max_rent," +
//                "      round(sum(rent_sum) / sum(units),2) as avg_rent," +
//                "      round(sum(rent_sum) / sum(sft_sum) ,2) as avg_rpfs," +
//                "      round(min(min_rent) / avg (sft_sum),2) * 10 as min_rpfs," +
//                "      round(max(max_rent) / avg (sft_sum),2) * 10 as max_rpfs," +
//                "      round(sum(day_listing_current_month)/ sum(units),2) as days ," +
//                "      sum(rent_listing_by_2025_07.units) units," +
//                "      count(distinct property_id) as total_properties from (" + query ") As views ";
//
//
//        return sqlClient.getAll(
//            query = query2,
//            params = emptyList(),
//        ) { result ->
//            buildAskingDto(result, idColumn)
//        }
//    }
////
////    private fun identifyRentListingViewsRange(dateFrom: LocalDate, dateTo: LocalDate): Sequence<String> {
////
////        val datesByMonth = generateSequence (dateFrom) {it.plusMonths(1)}
////            .takeWhile { it.isAfter(dateTo) }
////
////        val viewsToQuery = datesByMonth.map{
////                date -> "rent_listing_by_${date.year}_${"%02".format(date.monthValue)}"
////        }
////
////        return viewsToQuery
////
////    }
////    private fun getEffectiveMetrics(
////        groupers: List<String>,
////        idColumn: String,
////        propertyIdsPlaceholders: String,
////        metricsFiltersQuery: MetricsFiltersQuery,
////    ): List<EffectiveMetricDto> {
////        val query =
////            """
////            WITH
////            filtered_effective AS (
////                SELECT
////                    effective.rent,
////                    effective.rent_deposit,
////                    ${getGroupersSql(groupers, "effective")},
////                    ROW_NUMBER() OVER (PARTITION BY rent_listing_id ORDER BY date_to DESC) as rn
////                FROM effective_rent effective
////                ${metricsFiltersQuery.unitCondition?.getUnitConditionJoin(rentAlias = "effective") ?: ""}
////                WHERE effective.$idColumn IN ($propertyIdsPlaceholders)
////                    AND effective.date_from <= ?
////                    AND effective.date_to >= ?
////                    ${RentUtils.isActiveSql(tableName = "effective")}
////                    ${metricsFiltersQuery.rentListingType?.name?.let { " AND effective.type = '$it' "} ?: "" }
////                    ${metricsFiltersQuery.unitCondition?.getWhere() ?: ""}
////            )
////                SELECT
////                    ${getGroupersSql(groupers, "filtered_effective")},
////                    ${generateMetricsClause("rent", "filtered_effective", "effective_rent")}
////                FROM filtered_effective
////                WHERE rn = 1
////                GROUP BY ${getGroupersSql(groupers, "filtered_effective")}
////
////            """.trimIndent()
////
////        return sqlClient.getAll(
////            query = query,
////            params =
////                listOf<Any>()
////                    .asSequence()
////                    .plus(metricsFiltersQuery.ids)
////                    .plus(metricsFiltersQuery.dateTo)
////                    .plus(metricsFiltersQuery.dateFrom)
////                    .toList(),
////        ) { result ->
////            buildEffectiveDto(result, idColumn)
////        }
////    }
////
////    private fun getGroupersSql(
////        groupers: List<String>,
////        table: String? = null,
////    ) = """ ${groupers.joinToString(",") { "$table.$it".takeIf { table != null } ?: it }}"""
////
////    private fun buildEffectiveDto(
////        result: Map<String, Any>,
////        idColumn: String,
////    ): EffectiveMetricDto =
////        EffectiveMetricDto(
////            id = result.getString(idColumn),
////            bedrooms = result.getOptionalInt("bedrooms"),
////            bathrooms = result.getBigDecimal("bathrooms", 1),
////            floorPlan = result["floor_plan"]?.toString(),
////            effectiveRent = buildMetric(result, "effective_rent"),
////        )
////
////    private fun buildAskingDto(
////        result: Map<String, Any>,
////        idColumn: String,
////    ): AskingMetricDto =
////        AskingMetricDto(
////            id = result.getString(idColumn),
////            totalRecords = result.getInt("total_records"),
////            averageListingsDays = result.getBigDecimal("average_listing_days") ?: BigDecimal.ZERO,
////            unitsAvailable = result.getInt("units_available"),
////            askingRent = buildMetric(result, "rent")!!,
////            deposit = buildMetric(result, "rent_deposit"),
////            squareFootage = buildMetric(result, "unit_square_footage"),
////            bedrooms = result.getOptionalInt("bedrooms"),
////            bathrooms = result.getBigDecimal("bathrooms", 1),
////            floorPlan = result["floor_plan"]?.toString(),
////            totalUnits = result.getInt("total_units"),
////            unitId = result["type_id"].toString(),
////        )
////
////    private fun buildMetric(
////        result: Map<String, Any>,
////        prefix: String,
////    ): Metric? {
////        val min = result.getBigDecimal("${prefix}_min")
////        val max = result.getBigDecimal("${prefix}_max")
////        val average = result.getBigDecimal("${prefix}_average")
////        val median = result.getBigDecimal("${prefix}_median")
////
////        return takeIf { min != null && max != null && average != null && median != null }?.let {
////            Metric(min = min!!, max = max!!, average = average!!, median = median!!)
////        }
////    }
////
////    private fun buildMetricsClause(): List<String> =
////        listOf(
////            generateMetricsClause("rent", "filtered_listings", "rent"),
////            generateNullableMetricsClause("rent_deposit", "filtered_listings", "rent_deposit"),
////            generateNullableMetricsClause("unit_square_footage", "filtered_listings", "unit_square_footage"),
////        )
////
////    private fun generateMetricsClause(
////        grouper: String,
////        table: String,
////        alias: String,
////    ): String =
////        """
////        ROUND(AVG($table.$grouper), 2) AS ${alias}_average,
////        MIN($table.$grouper) AS ${alias}_min,
////        ROUND((percentile_cont(0.5) WITHIN GROUP
////            (ORDER BY $table.$grouper))::numeric, 2) AS ${alias}_median,
////        MAX($table.$grouper) AS ${alias}_max
////        """.trimIndent()
////
////    private fun generateNullableMetricsClause(
////        grouper: String,
////        table: String,
////        alias: String,
////    ): String =
////        """
////        ROUND(AVG(CASE WHEN $table.$grouper IS NOT NULL THEN $table.$grouper END), 2) AS ${alias}_average,
////        MIN($table.$grouper) AS ${alias}_min,
////        ROUND((percentile_cont(0.5) WITHIN GROUP
////            (ORDER BY CASE WHEN $table.$grouper IS NOT NULL THEN $table.$grouper END))::numeric,
////                2) AS ${alias}_median,
////        MAX($table.$grouper) AS ${alias}_max
////        """.trimIndent()
////}
