package com.keyway.adapters.repositories

import com.keyway.adapters.repositories.utils.RentUtils
import com.keyway.core.entities.RentListing
import com.keyway.core.ports.repositories.HistoricDataRepository
import com.keyway.kommons.db.SqlClient

class PostgresHistoricDataRepository(
    private val sqlClient: SqlClient,
) : HistoricDataRepository {
    override fun saveListing(listing: List<RentListing>) {
        if (listing.isEmpty()) return

        val valuesSql =
            listing.joinToString(", ") {
                "(?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)"
            }

        val fullQuery =
            """
                INSERT INTO historic_rent_listing (
                id, property_id, type, type_id, date_from, date_to, rent, record_source,
                zip_code, msa_code, unit_square_footage, bedrooms, bathrooms, floor_plan, available_in, rent_deposit,
                created_at, updated_at, is_active
            ) VALUES 
                $valuesSql
             ON CONFLICT (id) DO UPDATE SET
                property_id = EXCLUDED.property_id,
                type = EXCLUDED.type,
                type_id = EXCLUDED.type_id,
                record_source = EXCLUDED.record_source,
                date_from = EXCLUDED.date_from,
                date_to = EXCLUDED.date_to,
                rent = EXCLUDED.rent,
                zip_code = EXCLUDED.zip_code,
                msa_code = EXCLUDED.msa_code,
                unit_square_footage = EXCLUDED.unit_square_footage,
                bedrooms = EXCLUDED.bedrooms,
                bathrooms = EXCLUDED.bathrooms,
                floor_plan = EXCLUDED.floor_plan,
                available_in = EXCLUDED.available_in,
                rent_deposit = EXCLUDED.rent_deposit,
                created_at = EXCLUDED.created_at,
                updated_at = EXCLUDED.updated_at,
                is_active = EXCLUDED.is_active;
            """.trimIndent()

        sqlClient.update(
            query = fullQuery.trimIndent(),
            preparedStatementHandler = { ps ->
                RentUtils.listingPreparedStatementHandler(listing, ps)
            },
        )
    }
}
