package com.keyway.adapters.repositories.model

import com.keyway.core.entities.Money
import com.keyway.core.entities.RentListing
import com.keyway.core.entities.RentListingType
import java.math.BigDecimal
import java.time.LocalDate
import java.time.OffsetDateTime

data class RentListingDBModel(
    val id: String,
    val propertyId: String,
    val type: String,
    val typeId: String,
    val rent: BigDecimal,
    val dateFrom: LocalDate,
    val dateTo: LocalDate,
    val recordSource: String,
    val zipCode: String,
    val msaCode: String,
    val unitSquareFootage: BigDecimal?,
    val bedrooms: Int,
    val bathrooms: BigDecimal?,
    val floorPlan: String?,
    val availableIn: LocalDate?,
    val rentDeposit: BigDecimal?,
    val createdAt: OffsetDateTime,
    val updatedAt: OffsetDateTime,
) {
    fun toUnitRentListing(): RentListing =
        RentListing(
            id = id,
            propertyId = propertyId,
            type = RentListingType.valueOf(type),
            typeId = typeId,
            rent = Money.of(rent),
            dateTo = dateTo,
            dateFrom = dateFrom,
            recordSource = recordSource,
            zipCode = zipCode,
            msaCode = msaCode,
            unitSquareFootage = unitSquareFootage,
            bedroomsQuantity = bedrooms,
            bathroomsQuantity = bathrooms,
            floorPlan = floorPlan,
            availableIn = availableIn,
            rentDeposit = rentDeposit?.let { Money.of(it) },
            createdAt = createdAt,
            updateAt = updatedAt,
        )
}
