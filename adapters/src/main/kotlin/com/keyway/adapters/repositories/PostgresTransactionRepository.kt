package com.keyway.adapters.repositories

import com.keyway.core.ports.repositories.TransactionRepository
import com.keyway.kommons.db.transaction.datasource.TransactionalDataSource
import com.keyway.kommons.db.transaction.manager.TransactionManager
import com.keyway.kommons.db.transaction.manager.TransactionManager.useTransaction

class PostgresTransactionRepository(
    dataSource: TransactionalDataSource,
) : TransactionRepository {
    init {
        TransactionManager.initialize(dataSource)
    }

    override fun <T> executeTransaction(operation: () -> T): T =
        useTransaction {
            operation()
        }
}
