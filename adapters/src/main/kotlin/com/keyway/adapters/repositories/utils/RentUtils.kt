package com.keyway.adapters.repositories.utils

import com.keyway.core.entities.EffectiveRent
import com.keyway.core.entities.RentListing
import com.keyway.kommons.db.mapper.DatabaseMapper
import org.postgresql.util.PGobject
import java.sql.PreparedStatement
import java.sql.Types

object RentUtils {
    fun isActiveSql(
        tableName: String? = null,
        withAnd: Boolean = true,
    ) = """ ${" AND ".takeIf { withAnd } ?: "" } ${"$tableName.".takeUnless { tableName.isNullOrEmpty() } ?: ""}is_active = true """

    fun listingPreparedStatementHandler(
        rentListings: List<RentListing>,
        ps: PreparedStatement,
    ) {
        var parameterIndex = 1
        rentListings.forEach { rentListing ->
            with(rentListing) {
                ps.setString(parameterIndex++, id)
                ps.setString(parameterIndex++, propertyId)
                ps.setString(parameterIndex++, type.toString())
                ps.setString(parameterIndex++, typeId)
                ps.setObject(parameterIndex++, dateFrom, Types.DATE)
                ps.setObject(parameterIndex++, dateTo, Types.DATE)
                ps.setBigDecimal(parameterIndex++, rent.value)
                ps.setString(parameterIndex++, recordSource)
                ps.setString(parameterIndex++, zipCode)
                ps.setString(parameterIndex++, msaCode)
                ps.setBigDecimal(parameterIndex++, unitSquareFootage)
                ps.setInt(parameterIndex++, bedroomsQuantity)
                ps.setBigDecimal(parameterIndex++, bathroomsQuantity)
                ps.setString(parameterIndex++, floorPlan)
                availableIn?.let { ps.setObject(parameterIndex++, it, Types.DATE) }
                    ?: ps.setNull(parameterIndex++, Types.DATE)
                ps.setBigDecimal(parameterIndex++, rentDeposit?.value)
                ps.setObject(parameterIndex++, createdAt)
                ps.setObject(parameterIndex++, updateAt)
                ps.setBoolean(parameterIndex++, isActive)
            }
        }
    }

    fun effectivePreparedStatementHandler(
        effective: List<EffectiveRent>,
        ps: PreparedStatement,
    ) {
        var parameterIndex = 1
        effective.forEach { effectiveList ->
            with(effectiveList) {
                ps.setString(parameterIndex++, id)
                ps.setString(parameterIndex++, rentListingId)
                ps.setObject(
                    parameterIndex++,
                    PGobject().apply {
                        this.type = "jsonb"
                        this.value = DatabaseMapper.encode(concessionIds)
                    },
                )
                ps.setObject(parameterIndex++, dateFrom, Types.DATE)
                ps.setObject(parameterIndex++, dateTo, Types.DATE)
                ps.setBigDecimal(parameterIndex++, rent.value)
                ps.setBigDecimal(parameterIndex++, rentDeposit?.value)
                ps.setString(parameterIndex++, concessions)
                ps.setObject(parameterIndex++, createdAt)
                ps.setObject(parameterIndex++, updateAt)
                ps.setBoolean(parameterIndex++, isActive)

                ps.setString(parameterIndex++, propertyId)
                ps.setString(parameterIndex++, type.toString())
                ps.setString(parameterIndex++, typeId)
                ps.setString(parameterIndex++, recordSource)
                ps.setString(parameterIndex++, zipCode)
                ps.setString(parameterIndex++, msaCode)
                ps.setBigDecimal(parameterIndex++, unitSquareFootage)
                bedroomsQuantity?.let { ps.setInt(parameterIndex++, it) }
                    ?: ps.setNull(parameterIndex++, Types.INTEGER)
                ps.setBigDecimal(parameterIndex++, bathroomsQuantity)
                ps.setString(parameterIndex++, floorPlan)
            }
        }
    }
}
