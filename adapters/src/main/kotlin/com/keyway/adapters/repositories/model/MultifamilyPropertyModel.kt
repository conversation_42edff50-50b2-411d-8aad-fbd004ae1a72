package com.keyway.adapters.repositories.model

import com.fasterxml.jackson.databind.JsonNode
import com.keyway.core.entities.GeoPoint
import com.keyway.core.entities.MultifamilyProperty
import com.keyway.kommons.mapper.dataclass.mapTo
import java.math.BigDecimal
import java.time.LocalDate
import java.time.OffsetDateTime

data class MultifamilyPropertyModel(
    val id: String,
    val address: String,
    val city: String,
    val county: String?,
    val zipCode: Long,
    val state: String,
    val latitude: BigDecimal,
    val longitude: BigDecimal,
    val geolocation: JsonNode,
    val squareFootage: BigDecimal?,
    val squareFootagePerUnit: BigDecimal?,
    val sourceType: String?,
    val tractCode: Long?,
    val constructionYear: Int?,
    val renovationYear: Int?,
    val unitQuantity: Int?,
    val occupancyPercentage: BigDecimal?,
    val propertyAmenities: Set<String>? = emptySet(),
    val unitsAmenities: Set<String>? = emptySet(),
    val isActive: Boolean,
    val qualityOverallScore: BigDecimal?,
    val createdAt: OffsetDateTime,
    val updatedAt: OffsetDateTime,
    val lastSeen: LocalDate?,
    val stories: Int?,
    val propertyStyle: String?,
    val landSizeSqft: BigDecimal?,
    val housingSegment: Set<String> = emptySet(),
    val hasAffordableUnits: Boolean?,
) {
    fun toMultifamilyProperty(): MultifamilyProperty =
        this
            .mapTo<MultifamilyPropertyModel, MultifamilyProperty>(
                additions =
                    mapOf(
                        "location" to GeoPoint(latitude, longitude),
                        "geolocation" to this.geolocation["value"],
                    ),
            )
}
