package com.keyway.adapters.repositories.utils

import com.keyway.adapters.repositories.utils.IdTypeUtils.getSqlColumn
import com.keyway.core.dto.query.metrics.IdType
import com.keyway.core.entities.RentType
import java.time.LocalDate

object ViewsUtils {
    const val RENT_LISTING_VIEW = "rent_listing_by"
    const val EFFECTIVE_RENT_VIEW = "effective_rent_by"

    fun generateViewsNames(
        rentType: RentType,
        dateFrom: LocalDate,
        dateTo: LocalDate,
    ): List<String> =
        when (rentType) {
            RentType.ASKING -> generateViewsNames(RENT_LISTING_VIEW, dateFrom, dateTo)
            RentType.EFFECTIVE -> generateViewsNames(EFFECTIVE_RENT_VIEW, dateFrom, dateTo)
        }

    fun generateViewsNames(
        viewName: String,
        dateFrom: LocalDate,
        dateTo: LocalDate,
    ): List<String> =
        generateSequence(dateFrom) { it.plusMonths(1) }
            .takeWhile { it <= dateTo }
            .map { date -> """${viewName}_${date.year}_${date.monthValue.toString().padStart(2, '0')}""" }
            .toList()

    fun buildQueryCondition(
        idType: IdType,
        id: String,
        dateFrom: LocalDate,
        dateTo: LocalDate,
        bedrooms: Int? = null,
    ): String =
        """ 
                WHERE ${idType.getSqlColumn()} = '$id'
                AND date_of_record >= '$dateFrom' AND date_of_record <= '$dateTo'
                ${bedrooms?.let { " AND bedrooms = $it " } ?: ""}
        """.trimMargin()

    fun buildQueryCondition(
        idType: IdType,
        ids: Set<String>,
        dateFrom: LocalDate,
        dateTo: LocalDate,
        bedrooms: Int? = null,
    ): String =
        """ 
                WHERE ${idType.getSqlColumn()} IN (${ids.joinToString(",") { "'$it'" }})
                AND date_of_record BETWEEN '$dateFrom' AND '$dateTo'
                ${bedrooms?.let { " AND bedrooms = $it " } ?: ""}
        """.trimMargin()
}
