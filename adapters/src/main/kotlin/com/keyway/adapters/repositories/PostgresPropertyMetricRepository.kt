package com.keyway.adapters.repositories

import com.keyway.core.entities.property.PropertyLastSeenData
import com.keyway.core.ports.repositories.PropertyMetricsRepository
import com.keyway.kommons.db.SqlClient

class PostgresPropertyMetricRepository(
    private val sqlClient: SqlClient,
) : PropertyMetricsRepository {
    override fun getLastSeenByProperty(
        offset: Int,
        limit: Int,
        propertyIds: Set<String>,
        zipCodes: Set<String>,
    ): List<PropertyLastSeenData> =
        sqlClient.getAll(
            query =
                """
                SELECT property_id, MAX(date_to) as last_seen 
                FROM rent_listing
                WHERE 1 = 1 
                ${propertyIds.writeIfNotEmpty(
                    " AND property_id IN (${propertyIds.joinToString(","){"'$it'"}}) ",
                )}
                ${zipCodes.writeIfNotEmpty(
                    " AND zip_code IN (${zipCodes.joinToString(","){"'$it'"}}) ",
                )}
                GROUP BY property_id
                ORDER BY property_id
                OFFSET ?
                LIMIT ?
                """.trimIndent(),
            params = listOf(offset, limit),
            clazz = PropertyLastSeenData::class.java,
        )

    private fun Set<String>.writeIfNotEmpty(condition: String) = condition.takeIf { this.isNotEmpty() } ?: ""
}
