package com.keyway.adapters.repositories.utils

import com.keyway.kommons.db.mapper.DatabaseMapper
import org.postgresql.util.PGobject
import java.sql.PreparedStatement
import java.sql.Types
import java.time.LocalDate

fun PreparedStatement.setNullableInt(
    index: Int,
    value: Int?,
) {
    if (value != null) {
        setInt(index, value)
    } else {
        setNull(index, Types.INTEGER)
    }
}

fun PreparedStatement.setNullableBoolean(
    index: Int,
    value: Boolean?,
) {
    if (value != null) {
        setBoolean(index, value)
    } else {
        setNull(index, Types.BOOLEAN)
    }
}

fun List<Any>.toPGObject(): PGobject =
    this.let { values ->
        PGobject().apply {
            this.type = "jsonb"
            this.value = DatabaseMapper.encode(values)
        }
    }

fun PreparedStatement.setNullableString(
    parameterIndex: Int,
    value: String?,
) {
    value
        ?.also { setString(parameterIndex, value) }
        ?: setNull(parameterIndex, Types.VARCHAR)
}

fun PreparedStatement.setNullableLocalDate(
    parameterIndex: Int,
    value: LocalDate?,
) {
    value
        ?.also { setObject(parameterIndex, value, Types.DATE) }
        ?: setNull(parameterIndex, Types.DATE)
}
