package com.keyway.adapters.repositories

import com.keyway.adapters.repositories.utils.IdTypeUtils.getSqlColumn
import com.keyway.adapters.repositories.utils.RentUtils
import com.keyway.adapters.repositories.utils.UnitConditionUtils.getUnitConditionJoin
import com.keyway.adapters.repositories.utils.UnitConditionUtils.getWhere
import com.keyway.adapters.repositories.utils.ViewsUtils.buildQueryCondition
import com.keyway.adapters.repositories.utils.ViewsUtils.generateViewsNames
import com.keyway.core.dto.historical.HistoricalRentOutput
import com.keyway.core.dto.query.metrics.IdType
import com.keyway.core.entities.HistoricalPeriodicity
import com.keyway.core.entities.RentType
import com.keyway.core.entities.UnitCondition
import com.keyway.core.ports.repositories.HistoricalRentRepository
import com.keyway.kommons.db.SqlClient
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import java.math.BigDecimal
import java.time.LocalDate

class PostgresHistoricalRentRepository(
    private val sqlClient: SqlClient,
) : HistoricalRentRepository {
    private fun LocalDate.toSqlString(): String = "'$this'::date"

    private fun getDataSeries(
        dateFrom: LocalDate,
        dateTo: LocalDate,
        periodicity: HistoricalPeriodicity,
    ): String =
        when (periodicity) {
            HistoricalPeriodicity.DAILY -> """
            WITH RECURSIVE date_series AS (
                SELECT 
                    ${dateTo.toSqlString()} AS date_from,
                    ${dateTo.toSqlString()} AS date_to
                UNION ALL
                SELECT 
                    (date_from - INTERVAL '1 DAY')::DATE AS date_from,
                    (date_to - INTERVAL '1 DAY')::DATE AS date_to
                FROM date_series 
                WHERE date_from >= ${dateFrom.toSqlString()}
            )
        """

            HistoricalPeriodicity.WEEKLY -> """
            WITH RECURSIVE date_series AS (
                SELECT 
                    DATE_TRUNC('week', ${dateTo.toSqlString()})::DATE AS date_from,
                    (DATE_TRUNC('week', ${dateTo.toSqlString()}) + INTERVAL '6 DAYS')::DATE AS date_to
                UNION ALL
                SELECT 
                    (date_from - INTERVAL '7 DAYS')::DATE AS date_from,
                    (date_to - INTERVAL '7 DAYS')::DATE AS date_to
                FROM date_series 
                WHERE date_from >= DATE_TRUNC('week', ${dateFrom.toSqlString()})::DATE
            )
        """

            HistoricalPeriodicity.MONTHLY -> """
            WITH RECURSIVE date_series AS (
                SELECT 
                    DATE_TRUNC('month', ${dateTo.toSqlString()})::DATE AS date_from,
                    (DATE_TRUNC('month', ${dateTo.toSqlString()}) + INTERVAL '1 MONTH - 1 DAY')::DATE AS date_to
                UNION ALL
                SELECT 
                    DATE_TRUNC('month', date_from - INTERVAL '1 MONTH')::DATE AS date_from,
                    (DATE_TRUNC('month', date_from - INTERVAL '1 MONTH') + INTERVAL '1 MONTH - 1 DAY')::DATE AS date_to
                FROM date_series
                WHERE date_from >=  DATE_TRUNC('month', ${dateFrom.toSqlString()})::DATE
            )
        """
        }

    override suspend fun getHistoricalRents(
        ids: Set<String>,
        idType: IdType,
        dateFrom: LocalDate,
        dateTo: LocalDate,
        periodicity: HistoricalPeriodicity,
        rentType: RentType,
        bedrooms: Int?,
        bathrooms: BigDecimal?,
        unitCondition: UnitCondition?,
    ): List<HistoricalRentOutput> =
        withContext(Dispatchers.IO) {
            when (idType) {
                IdType.ZIP_CODE, IdType.MSA ->
                    getGeoData(
                        ids,
                        idType,
                        dateFrom,
                        dateTo,
                        periodicity,
                        rentType,
                        bedrooms,
                    )
                else ->
                    getPropertyData(
                        ids.joinToString(",") { "'$it'" },
                        idType,
                        dateFrom,
                        dateTo,
                        periodicity,
                        rentType,
                        bedrooms,
                        bathrooms,
                        unitCondition,
                    )
            }
        }

    private fun getGeoData(
        ids: Set<String>,
        idType: IdType,
        dateFrom: LocalDate,
        dateTo: LocalDate,
        periodicity: HistoricalPeriodicity,
        rentType: RentType,
        bedrooms: Int?,
    ): List<HistoricalRentOutput> {
        val condition = buildQueryCondition(idType, ids, dateFrom, dateTo, bedrooms)
        val query =
            """
            ${getDataSeries(dateFrom, dateTo, periodicity)},
                filtered_data AS (
                    ${generateViewsNames(rentType, dateFrom, dateTo)
                .joinToString(" UNION ALL ") { "SELECT * FROM $it $condition"}}
                )
                 SELECT 
                ${idType.getSqlColumn()} as id, 
                date_series.date_from as date_from,
                date_series.date_to as date_to,
                bedrooms, 
                ROUND(SUM(sft_sum) / SUM(total_records), 2) AS square_footage_average,
                ROUND(SUM(rent_sum) / SUM(total_records), 2) AS rent_average
            FROM filtered_data join date_series 
                ON filtered_data.date_of_record BETWEEN date_series.date_from AND date_series.date_to 
            GROUP BY ${idType.getSqlColumn()}, bedrooms, date_series.date_from, date_series.date_to
            """.trimIndent()

        return sqlClient.getAll(
            query = query,
            params = emptyList(),
            clazz = HistoricalRentOutput::class.java,
        )
    }

    private fun getPropertyData(
        sqlIds: String,
        idType: IdType,
        dateFrom: LocalDate,
        dateTo: LocalDate,
        periodicity: HistoricalPeriodicity,
        rentType: RentType,
        bedrooms: Int?,
        bathrooms: BigDecimal?,
        unitCondition: UnitCondition?,
    ): List<HistoricalRentOutput> {
        val query =
            """
                ${getDataSeries(dateFrom, dateTo, periodicity)},
                filtered_data AS (
                    ${buildQuery(
                when (rentType) {
                    RentType.ASKING -> "rent_listing"
                    RentType.EFFECTIVE -> "effective_rent"
                },
                idType,
                sqlIds,
                dateTo,
                dateFrom,
                bedrooms,
                bathrooms,
                unitCondition = unitCondition,
            )}   
                )
                 SELECT 
                id as id, 
                date_series.date_from as date_from,
                date_series.date_to as date_to,
                bedrooms, 
                bathrooms,
                ROUND(AVG(CASE WHEN unit_square_footage IS NOT NULL THEN unit_square_footage END), 2) AS square_footage_average,
                ROUND(AVG(rent), 2) AS rent_average,
                ROUND((percentile_cont(0.5) WITHIN GROUP (ORDER BY rent))::numeric, 2) AS rent_median
            FROM filtered_data join date_series 
                ON filtered_data.date_from <= date_series.date_to 
                    AND filtered_data.date_to >= date_series.date_from
            GROUP BY id, bedrooms, bathrooms, date_series.date_from, date_series.date_to
            """.trimIndent()

        return sqlClient.getAll(
            query = query,
            params = emptyList(),
            clazz = HistoricalRentOutput::class.java,
        )
    }

    private fun buildQuery(
        tableName: String,
        idType: IdType,
        sqlIds: String,
        dateTo: LocalDate,
        dateFrom: LocalDate,
        bedrooms: Int?,
        bathrooms: BigDecimal?,
        unitCondition: UnitCondition?,
    ) = """
        SELECT 
        $tableName.${idType.getSqlColumn()} as id,
        $tableName.id as listing_id,
        $tableName.property_id, 
        $tableName.bedrooms,
        $tableName.bathrooms, 
        $tableName.unit_square_footage, 
        $tableName.rent,
        $tableName.type,
        $tableName.date_from,
        $tableName.date_to
           FROM $tableName
           ${unitCondition?.getUnitConditionJoin(rentAlias = tableName) ?: ""}
           WHERE  $tableName.${idType.getSqlColumn()} IN ($sqlIds)
             AND date_from <= ${dateTo.toSqlString()}
             AND date_to >= ${dateFrom.toSqlString()}
             ${RentUtils.isActiveSql(tableName = tableName)}
             ${bedrooms?.let { " AND $tableName.bedrooms = $it " } ?: ""}
             ${bathrooms?.let { " AND $tableName.bathrooms = $it" } ?: ""}
             ${unitCondition?.getWhere() ?: ""}
        """.trimIndent()
}
