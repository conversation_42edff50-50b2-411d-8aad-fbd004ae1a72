package com.keyway.adapters.repositories

import com.keyway.adapters.repositories.utils.IdTypeUtils.getSqlColumn
import com.keyway.adapters.repositories.utils.RentUtils
import com.keyway.adapters.repositories.utils.UnitConditionUtils.getUnitConditionJoin
import com.keyway.adapters.repositories.utils.UnitConditionUtils.getWhere
import com.keyway.core.dto.historical.HistoricalRentOutput
import com.keyway.core.dto.query.metrics.IdType
import com.keyway.core.entities.HistoricalRentPeriodicity
import com.keyway.core.entities.RentType
import com.keyway.core.entities.UnitCondition
import com.keyway.core.ports.repositories.HistoricalRentRepository
import com.keyway.core.utils.DateUtils.toEndOfMonth
import com.keyway.kommons.db.SqlClient
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.async
import kotlinx.coroutines.runBlocking
import kotlinx.coroutines.withContext
import java.math.BigDecimal
import java.math.RoundingMode
import java.time.LocalDate

class PostgresHistoricalRentRepository(
    private val sqlClient: SqlClient,
) : HistoricalRentRepository {
    private fun LocalDate.toSqlString(): String = "'$this'::date"

    private fun getDataSeries(
        dateFrom: LocalDate,
        dateTo: LocalDate,
        periodicity: HistoricalRentPeriodicity,
    ): String =
        when (periodicity) {
            HistoricalRentPeriodicity.DAILY -> """
            WITH RECURSIVE date_series AS (
                SELECT 
                    ${dateTo.toSqlString()} AS date_from,
                    ${dateTo.toSqlString()} AS date_to
                UNION ALL
                SELECT 
                    (date_from - INTERVAL '1 DAY')::DATE AS date_from,
                    (date_to - INTERVAL '1 DAY')::DATE AS date_to
                FROM date_series 
                WHERE date_from >= ${dateFrom.toSqlString()}
            )
        """

            HistoricalRentPeriodicity.WEEKLY -> """
            WITH RECURSIVE date_series AS (
                SELECT 
                    DATE_TRUNC('week', ${dateTo.toSqlString()})::DATE AS date_from,
                    (DATE_TRUNC('week', ${dateTo.toSqlString()}) + INTERVAL '6 DAYS')::DATE AS date_to
                UNION ALL
                SELECT 
                    (date_from - INTERVAL '7 DAYS')::DATE AS date_from,
                    (date_to - INTERVAL '7 DAYS')::DATE AS date_to
                FROM date_series 
                WHERE date_from >= DATE_TRUNC('week', ${dateFrom.toSqlString()})::DATE
            )
        """

            HistoricalRentPeriodicity.MONTHLY -> """
            WITH RECURSIVE date_series AS (
                SELECT 
                    DATE_TRUNC('month', ${dateTo.toSqlString()})::DATE AS date_from,
                    (DATE_TRUNC('month', ${dateTo.toSqlString()}) + INTERVAL '1 MONTH - 1 DAY')::DATE AS date_to
                UNION ALL
                SELECT 
                    DATE_TRUNC('month', date_from - INTERVAL '1 MONTH')::DATE AS date_from,
                    (DATE_TRUNC('month', date_from - INTERVAL '1 MONTH') + INTERVAL '1 MONTH - 1 DAY')::DATE AS date_to
                FROM date_series
                WHERE date_from >=  DATE_TRUNC('month', ${dateFrom.toSqlString()})::DATE
            )
        """
        }

    override suspend fun getHistoricalRents(
        ids: Set<String>,
        idType: IdType,
        dateFrom: LocalDate,
        dateTo: LocalDate,
        periodicity: HistoricalRentPeriodicity,
        rentType: RentType,
        bedrooms: Int?,
        bathrooms: BigDecimal?,
        unitCondition: UnitCondition?,
    ): List<HistoricalRentOutput> =
        withContext(Dispatchers.IO) {
            val sqlIds = ids.joinToString(",") { "'$it'" }

            when (idType) {
                IdType.ZIP_CODE -> runBlocking { getZipData(sqlIds, dateFrom, dateTo) }
                IdType.MSA -> emptyList()
                else ->
                    getPropertyData(
                        sqlIds,
                        idType,
                        dateFrom,
                        dateTo,
                        periodicity,
                        rentType,
                        bedrooms,
                        bathrooms,
                        unitCondition,
                    )
            }
        }

    private fun getPropertyData(
        sqlIds: String,
        idType: IdType,
        dateFrom: LocalDate,
        dateTo: LocalDate,
        periodicity: HistoricalRentPeriodicity,
        rentType: RentType,
        bedrooms: Int?,
        bathrooms: BigDecimal?,
        unitCondition: UnitCondition?,
    ): List<HistoricalRentOutput> {
        val query =
            """
                ${getDataSeries(dateFrom, dateTo, periodicity)},
                filtered_data AS (
                    ${buildQuery(
                when (rentType) {
                    RentType.ASKING -> "rent_listing"
                    RentType.EFFECTIVE -> "effective_rent"
                },
                idType,
                sqlIds,
                dateTo,
                dateFrom,
                bedrooms,
                bathrooms,
                unitCondition = unitCondition,
            )}   
                )
                 SELECT 
                id as id, 
                date_series.date_from as date_from,
                date_series.date_to as date_to,
                bedrooms, 
                bathrooms,
                ROUND(AVG(CASE WHEN unit_square_footage IS NOT NULL THEN unit_square_footage END), 2) AS square_footage_average,
                ROUND(AVG(rent), 2) AS rent_average,
                ROUND((percentile_cont(0.5) WITHIN GROUP (ORDER BY rent))::numeric, 2) AS rent_median
            FROM filtered_data join date_series 
                ON filtered_data.date_from <= date_series.date_to 
                    AND filtered_data.date_to >= date_series.date_from
            GROUP BY id, bedrooms, bathrooms, date_series.date_from, date_series.date_to
            """.trimIndent()

        return sqlClient.getAll(
            query = query,
            params = emptyList(),
            clazz = HistoricalRentOutput::class.java,
        )
    }

    private fun getAskingZipData(
        ids: String,
        dateFrom: LocalDate,
        dateTo: LocalDate,
    ): List<HistoricalRentOutput> {
        val rentListingQuery =
            """
            SELECT zip_code as id,
                   avg(case when unit_square_footage is not null then (rent / unit_square_footage) end)::numeric as rent_avg,
                   percentile_cont(0.5) WITHIN GROUP 
                       (ORDER BY case when unit_square_footage is not null then (rent / unit_square_footage) end)::numeric AS rent_median,
                   avg(unit_square_footage)::numeric as sft,
                   date_part('month', rl.date_from) as month,
                   date_part('year', rl.date_from) as year
            FROM rent_listing rl
            WHERE zip_code IN ($ids)
              AND rl.date_from BETWEEN ${dateFrom.toSqlString()} AND ${dateTo.toSqlString()}
              AND rl.is_active = true
            GROUP BY zip_code, date_part('month', rl.date_from), date_part('year', rl.date_from)
            HAVING AVG(unit_square_footage) IS NOT NULL
            """.trimIndent()

        return executeQuery(query = rentListingQuery)
    }

    private fun getHistoricalZipData(
        ids: String,
        dateFrom: LocalDate,
        dateTo: LocalDate,
    ): List<HistoricalRentOutput> {
        val historicRentListingQuery =
            """
            SELECT zip_code as id,
                   avg(rent_avg_psf)::numeric as rent_avg,
                   avg(rent_median_psf)::numeric as rent_median,
                   avg(square_footage_avg)::numeric as sft,
                   month,
                   year
            FROM zip_historic_rent
            WHERE zip_code IN ($ids)
               AND date_from BETWEEN ${dateFrom.toSqlString()} AND ${dateTo.toSqlString()}
            GROUP BY zip_code, month, year 
            """.trimIndent()

        return executeQuery(query = historicRentListingQuery)
    }

    private fun executeQuery(query: String): List<HistoricalRentOutput> =
        sqlClient.getAll(
            query = query,
            params = emptyList(),
        ) { response ->
            val year = response["year"].getInt()
            val month = response["month"].getInt()
            val rentAvgPsf = response["rent_avg"] as BigDecimal
            val rentMedianPsf = response["rent_median"] as BigDecimal
            val sft = response["sft"] as BigDecimal

            val initialDate = LocalDate.of(year, month, 1)
            HistoricalRentOutput(
                id = response["id"] as String,
                dateFrom = initialDate,
                dateTo = initialDate.toEndOfMonth(),
                rentAverage = rentAvgPsf * sft,
                rentMedian = rentMedianPsf * sft,
                squareFootageAverage = sft,
                bedrooms = 1,
                bathrooms = BigDecimal.ONE,
            )
        }

    private fun Any?.getInt(): Int =
        when (this) {
            is Int -> this
            is Double -> this.toInt()
            is Float -> this.toInt()
            is Long -> this.toInt()
            is Short -> this.toInt()
            is BigDecimal -> this.toInt()
            else -> throw IllegalArgumentException()
        }

    private fun List<BigDecimal>.average(): BigDecimal {
        if (this.isEmpty()) {
            return BigDecimal.ZERO
        }
        val sum = this.reduce { acc, value -> acc + value }
        return sum.divide(BigDecimal(this.size), 10, RoundingMode.HALF_UP)
    }

    private suspend fun getZipData(
        ids: String,
        dateFrom: LocalDate,
        dateTo: LocalDate,
    ): List<HistoricalRentOutput> =
        withContext(Dispatchers.IO) {
            val askingDeferred = async { getAskingZipData(ids, dateFrom, dateTo) }
            val historicalDeferred = async { getHistoricalZipData(ids, dateFrom, dateTo) }
            val askingResult = askingDeferred.await()
            val historicalResult = historicalDeferred.await()

            (askingResult + historicalResult)
                .groupBy { Triple(it.id, it.dateFrom.year, it.dateFrom.month) }
                .mapValues { (_, group) ->
                    val first = group.first()
                    HistoricalRentOutput(
                        id = first.id,
                        rentAverage = group.map { it.rentAverage }.average(),
                        rentMedian = group.map { it.rentMedian }.average(),
                        squareFootageAverage = group.mapNotNull { it.squareFootageAverage }.average(),
                        dateFrom = first.dateFrom,
                        dateTo = first.dateTo,
                        bathrooms = first.bathrooms,
                        bedrooms = first.bedrooms,
                    )
                }.values
                .toList()
                .sortedBy { it.dateFrom }
        }

    private fun buildQuery(
        tableName: String,
        idType: IdType,
        sqlIds: String,
        dateTo: LocalDate,
        dateFrom: LocalDate,
        bedrooms: Int?,
        bathrooms: BigDecimal?,
        unitCondition: UnitCondition?,
    ) = """
        SELECT 
        $tableName.${idType.getSqlColumn()} as id,
        $tableName.id as listing_id,
        $tableName.property_id, 
        $tableName.bedrooms,
        $tableName.bathrooms, 
        $tableName.unit_square_footage, 
        $tableName.rent,
        $tableName.type,
        $tableName.date_from,
        $tableName.date_to
           FROM $tableName
           ${unitCondition?.getUnitConditionJoin(rentAlias = tableName) ?: ""}
           WHERE  $tableName.${idType.getSqlColumn()} IN ($sqlIds)
             AND date_from <= ${dateTo.toSqlString()}
             AND date_to >= ${dateFrom.toSqlString()}
             ${RentUtils.isActiveSql(tableName = tableName)}
             ${bedrooms?.let { " AND $tableName.bedrooms = $it " } ?: ""}
             ${bathrooms?.let { " AND $tableName.bathrooms = $it" } ?: ""}
             ${unitCondition?.getWhere() ?: ""}
        """.trimIndent()
}
