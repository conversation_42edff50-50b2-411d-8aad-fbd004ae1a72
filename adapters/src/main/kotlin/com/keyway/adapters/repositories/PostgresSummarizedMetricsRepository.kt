package com.keyway.adapters.repositories

import com.keyway.adapters.repositories.utils.IdTypeUtils.getSqlColumn
import com.keyway.adapters.repositories.utils.PostgresQueryUtils.getBigDecimal
import com.keyway.adapters.repositories.utils.PostgresQueryUtils.getInt
import com.keyway.adapters.repositories.utils.PostgresQueryUtils.getOptionalInt
import com.keyway.adapters.repositories.utils.PostgresQueryUtils.getString
import com.keyway.adapters.repositories.utils.RentUtils
import com.keyway.adapters.repositories.utils.UnitConditionUtils.getUnitConditionJoin
import com.keyway.adapters.repositories.utils.UnitConditionUtils.getWhere
import com.keyway.core.dto.EffectiveMetricDto
import com.keyway.core.dto.GeoAskingMetricDto
import com.keyway.core.dto.Metric
import com.keyway.core.dto.query.metrics.IdType
import com.keyway.core.dto.query.metrics.MetricsFiltersQuery
import com.keyway.core.entities.metric.MetricType
import com.keyway.core.ports.repositories.SummarizedMetricsRepository
import com.keyway.kommons.db.SqlClient
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.async
import kotlinx.coroutines.withContext
import java.math.BigDecimal

class PostgresSummarizedMetricsRepository(
    private val sqlClient: SqlClient,
) : SummarizedMetricsRepository {
    companion object {
        val groupersMap: Map<MetricType, List<String>> =
            mapOf(
                MetricType.BY_ID to emptyList(),
                MetricType.BEDROOMS to listOf("bedrooms"),
                MetricType.UNIT_MIX to listOf("bedrooms", "bathrooms"),
                MetricType.FLOOR_PLAN to listOf("floor_plan", "bedrooms", "bathrooms"),
                MetricType.UNITS to listOf("type_id", "floor_plan", "bedrooms", "bathrooms", "unit_square_footage"),
            )
    }

    override suspend fun aggregateMetrics(metricsFiltersQuery: MetricsFiltersQuery): List<GeoMetricDto> =
        withContext(Dispatchers.IO) {
            val propertyIdsPlaceholders = metricsFiltersQuery.ids.joinToString(",") { "?" }
            val idColumn = metricsFiltersQuery.idType.getSqlColumn()
            val groupers = listOf(idColumn).plus(groupersMap.getValue(metricsFiltersQuery.type))

            val askingDeferred = async { getAskingMetrics(groupers, idColumn, propertyIdsPlaceholders, metricsFiltersQuery) }
        //    val effectiveDeferred = async { getEffectiveMetrics(groupers, idColumn, propertyIdsPlaceholders, metricsFiltersQuery) }
            val askingResult = askingDeferred.await()
       //     val effectiveResult = effectiveDeferred.await()

        //    val effectiveMap = effectiveResult.associateBy { it.code }
            askingResult.map { ask ->
                GeoMetricDto(ask,null )
            }
        }

    private fun getAskingMetrics(
        groupers: List<String>,
        idColumn: String,
        propertyIdsPlaceholders: String,
        metricsFiltersQuery: MetricsFiltersQuery,
    ): List<GeoAskingMetricDto> {



        val datesByMonth = generateSequence (metricsFiltersQuery.dateFrom) {it.plusMonths(1)}
            .takeWhile { it <= metricsFiltersQuery.dateTo }

        val viewsToQuery = datesByMonth.map{
                date ->  String.format("rent_listing_by_%d_%02d",date.year, date.monthValue)}
        .toList()

        val idsSqlString = metricsFiltersQuery.ids.joinToString(", ") { "'$it'" }
        val condition = String.format(" where %s in (%s) AND date_of_record BETWEEN '%s' AND '%s'", metricsFiltersQuery.idType, idsSqlString, metricsFiltersQuery.dateFrom.toString(), metricsFiltersQuery.dateTo.toString())
        val viewsQuery = viewsToQuery.joinToString(" UNION ALL ") { "SELECT * from $it $condition" };

        var  query = "WITH filtered_rent_listings as (" +viewsQuery +")"

          query += ", filtered_with_units AS ( SELECT " +
            " property_id, min_rent, max_rent, rent_sum, min_sft, max_sft, sft_sum, total_records,UNNEST(units) AS unit FROM filtered_rent_listings )"

          query += "SELECT" +
              "  count (distinct property_id) as total_properties," +
              "  MIN(min_rent) AS rent_min," +
              "  MAX(max_rent) AS rent_max," +
              "  ROUND(SUM(rent_sum) / SUM(total_records), 2) AS rent," +
              "  MIN(min_sft) AS sft_min," +
              "  MAX(max_sft) AS sft_max," +
              "  ROUND(SUM(sft_sum) / SUM(total_records), 2) AS avg_sft," +
              "  count(*) AS total_records," +
              "  COUNT(DISTINCT unit) AS total_units" +
              " FROM filtered_with_units"

        return sqlClient.getAll(
            query = query,
            params = emptyList(),
        ) { result ->
            buildAskingDto2(result, metricsFiltersQuery.idType, metricsFiltersQuery.ids )
        }
    }


    private fun getEffectiveMetrics(
        groupers: List<String>,
        idColumn: String,
        propertyIdsPlaceholders: String,
        metricsFiltersQuery: MetricsFiltersQuery,
    ): List<EffectiveMetricDto> {
        val query =
            """
            WITH 
            filtered_effective AS (
                SELECT 
                    effective.rent,
                    effective.rent_deposit,
                    ${getGroupersSql(groupers, "effective")},
                    ROW_NUMBER() OVER (PARTITION BY rent_listing_id ORDER BY date_to DESC) as rn
                FROM effective_rent effective
                ${metricsFiltersQuery.unitCondition?.getUnitConditionJoin(rentAlias = "effective") ?: ""}
                WHERE effective.$idColumn IN ($propertyIdsPlaceholders)
                    AND effective.date_from <= ? 
                    AND effective.date_to >= ?
                    ${RentUtils.isActiveSql(tableName = "effective")}
                    ${metricsFiltersQuery.rentListingType?.name?.let { " AND effective.type = '$it' "} ?: "" }
                    ${metricsFiltersQuery.unitCondition?.getWhere() ?: ""}
            ) 
                SELECT 
                    ${getGroupersSql(groupers, "filtered_effective")},
                    ${generateMetricsClause("rent", "filtered_effective", "effective_rent")}
                FROM filtered_effective
                WHERE rn = 1
                GROUP BY ${getGroupersSql(groupers, "filtered_effective")}
            
            """.trimIndent()

        return sqlClient.getAll(
            query = query,
            params =
                listOf<Any>()
                    .asSequence()
                    .plus(metricsFiltersQuery.ids)
                    .plus(metricsFiltersQuery.dateTo)
                    .plus(metricsFiltersQuery.dateFrom)
                    .toList(),
        ) { result ->
            buildEffectiveDto(result, idColumn)
        }
    }

    private fun getGroupersSql(
        groupers: List<String>,
        table: String? = null,
    ) = """ ${groupers.joinToString(",") { "$table.$it".takeIf { table != null } ?: it }}"""

    private fun buildEffectiveDto(
        result: Map<String, Any>,
        idColumn: String,
    ): EffectiveMetricDto =
        EffectiveMetricDto(
            id = result.getString(idColumn),
            bedrooms = result.getOptionalInt("bedrooms"),
            bathrooms = result.getBigDecimal("bathrooms", 1),
            floorPlan = result["floor_plan"]?.toString(),
            effectiveRent = buildMetric(result, "effective_rent"),
        )

    private fun buildAskingDto2(
        result: Map<String, Any>,
        filteredColumn:  IdType,
        filteredValues: Set<String>,
    ): GeoAskingMetricDto =
        GeoAskingMetricDto(
            id = filteredValues.toString(),
            idType = filteredColumn.toString(),
            totalRecords = result.getInt("total_records"),
            averageListingsDays = result.getBigDecimal("average_listing_days") ?: BigDecimal.ZERO,
            askingRent = buildMetric(result, "rent")!!,
            squareFootage = buildMetric(result, "unit_square_footage"),
            totalUnits = result.getInt("total_units"),
            totalProperties =  result.getInt("total_properties"),
        )

    private fun buildMetric(
        result: Map<String, Any>,
        prefix: String,
    ): Metric? {
        val min = result.getBigDecimal("${prefix}_min")
        val max = result.getBigDecimal("${prefix}_max")
        val average = result.getBigDecimal("${prefix}_average")
        val median = result.getBigDecimal("${prefix}_median")

        return takeIf { min != null && max != null && average != null && median != null }?.let {
            Metric(min = min!!, max = max!!, average = average!!, median = median!!)
        }
    }

    private fun buildMetricsClause(): List<String> =
        listOf(
            generateMetricsClause("rent", "filtered_listings", "rent"),
            generateNullableMetricsClause("rent_deposit", "filtered_listings", "rent_deposit"),
            generateNullableMetricsClause("unit_square_footage", "filtered_listings", "unit_square_footage"),
        )

    private fun generateMetricsClause(
        grouper: String,
        table: String,
        alias: String,
    ): String =
        """
        ROUND(AVG($table.$grouper), 2) AS ${alias}_average,
        MIN($table.$grouper) AS ${alias}_min,
        ROUND((percentile_cont(0.5) WITHIN GROUP 
            (ORDER BY $table.$grouper))::numeric, 2) AS ${alias}_median,
        MAX($table.$grouper) AS ${alias}_max
        """.trimIndent()

    private fun generateNullableMetricsClause(
        grouper: String,
        table: String,
        alias: String,
    ): String =
        """
        ROUND(AVG(CASE WHEN $table.$grouper IS NOT NULL THEN $table.$grouper END), 2) AS ${alias}_average,
        MIN($table.$grouper) AS ${alias}_min,
        ROUND((percentile_cont(0.5) WITHIN GROUP 
            (ORDER BY CASE WHEN $table.$grouper IS NOT NULL THEN $table.$grouper END))::numeric, 
                2) AS ${alias}_median,
        MAX($table.$grouper) AS ${alias}_max
        """.trimIndent()
}
