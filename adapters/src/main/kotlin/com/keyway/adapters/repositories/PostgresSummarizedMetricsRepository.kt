package com.keyway.adapters.repositories

import com.keyway.adapters.repositories.utils.IdTypeUtils.getSqlColumn
import com.keyway.adapters.repositories.utils.PostgresQueryUtils.getBigDecimal
import com.keyway.adapters.repositories.utils.PostgresQueryUtils.getInt
import com.keyway.adapters.repositories.utils.PostgresQueryUtils.getOptionalInt
import com.keyway.core.dto.GeoAskingMetricDto
import com.keyway.core.dto.GeoEffectiveMetricDto
import com.keyway.core.dto.GeoMetric
import com.keyway.core.dto.SummarizedMetricDto
import com.keyway.core.dto.query.metrics.IdType
import com.keyway.core.dto.query.metrics.MetricsFiltersQuery
import com.keyway.core.entities.metric.MetricType
import com.keyway.core.ports.repositories.SummarizedMetricsRepository
import com.keyway.kommons.db.SqlClient
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.async
import kotlinx.coroutines.withContext
import java.math.BigDecimal

class PostgresSummarizedMetricsRepository(
    private val sqlClient: SqlClient,
) : SummarizedMetricsRepository {
    override suspend fun aggregateMetrics(metricsFiltersQuery: MetricsFiltersQuery): List<SummarizedMetricDto> =
        withContext(Dispatchers.IO) {
            val askingDeferred = async { getAskingMetrics(metricsFiltersQuery) }
            val effectiveDeferred = async { getEffectiveMetrics(metricsFiltersQuery) }
            val askingResult = askingDeferred.await()
            val effectiveResult = effectiveDeferred.await()

            val effectiveMap = effectiveResult.associateBy { it.code }
            askingResult.map { ask ->
                SummarizedMetricDto(ask, effectiveMap[ask.code])
            }
        }

    private fun getAskingMetrics(metricsFiltersQuery: MetricsFiltersQuery): List<GeoAskingMetricDto> {
        val query = buildAskingMetricsQuery("rent_listing", metricsFiltersQuery)

        return sqlClient.getAll(
            query = query,
            params = emptyList(),
        ) { result ->
            buildGeoAskingDto(result, metricsFiltersQuery.idType, metricsFiltersQuery.ids)
        }
    }

    private fun buildAskingMetricsQuery(
        viewName: String,
        metricsFiltersQuery: MetricsFiltersQuery,
    ): String {
        val datesByMonth =
            generateSequence(metricsFiltersQuery.dateFrom) { it.plusMonths(1) }
                .takeWhile { it <= metricsFiltersQuery.dateTo }

        val viewsToQuery =
            datesByMonth
                .map { date ->
                    String.format("%s_by_%d_%02d", viewName, date.year, date.monthValue)
                }.toList()

        val idsSqlString = metricsFiltersQuery.ids.joinToString(", ") { "'$it'" }
        val condition =
            String.format(
                " where %s in (%s) AND date_of_record BETWEEN '%s' AND '%s'",
                metricsFiltersQuery.idType.getSqlColumn(),
                idsSqlString,
                metricsFiltersQuery.dateFrom.toString(),
                metricsFiltersQuery.dateTo.toString(),
            )
        val viewsQuery = viewsToQuery.joinToString(" UNION ALL ") { "SELECT * from $it $condition" }

        val allowedGroupBys = if (metricsFiltersQuery.type == MetricType.BEDROOMS) "bedrooms," else ""

        var query =
            "WITH filtered_rent_listings as (" + viewsQuery + ")" +
                ", filtered_with_units AS ( SELECT " + allowedGroupBys +
                " property_id, min_rent, max_rent, rent_sum, min_sft, max_sft, sft_sum, total_records, day_listing_current_month, " +
                "UNNEST(units) AS unit FROM filtered_rent_listings ) SELECT " + allowedGroupBys +
                "  count (distinct property_id) as total_properties," +
                "  MIN(min_rent) AS rent_min," +
                "  MAX(max_rent) AS rent_max," +
                "  ROUND(SUM(rent_sum) / SUM(total_records), 2) AS rent," +
                "  MIN(min_sft) AS unit_square_footage_min," +
                "  MAX(max_sft) AS unit_square_footage_max," +
                "  ROUND(SUM(sft_sum) / SUM(total_records), 2) AS unit_square_footage," +
                "  count(*) AS total_records," +
                "  COUNT(DISTINCT unit) AS total_units," +
                "  AVG(day_listing_current_month) as average_listing_days" +
                " FROM filtered_with_units "

        if (metricsFiltersQuery.type == MetricType.BEDROOMS) query += "GROUP BY bedrooms"

        return query
    }

    private fun getEffectiveMetrics(metricsFiltersQuery: MetricsFiltersQuery): List<GeoEffectiveMetricDto> {
        val query = buildAskingMetricsQuery("effective_rent", metricsFiltersQuery)

        return sqlClient.getAll(
            query = query,
            params = emptyList(),
        ) { result ->
            buildGeoEffectiveDto(result, metricsFiltersQuery.idType, metricsFiltersQuery.ids)
        }
    }

    private fun buildGeoEffectiveDto(
        result: Map<String, Any>,
        idType: IdType,
        idColumn: Set<String>,
    ): GeoEffectiveMetricDto =
        GeoEffectiveMetricDto(
            id = idColumn.toString(),
            idType = idType.toString(),
            bedrooms = result.getOptionalInt("bedrooms"),
            effectiveRent = buildGeoMetric(result, "rent"),
        )

    private fun buildGeoAskingDto(
        result: Map<String, Any>,
        filteredColumn: IdType,
        filteredValues: Set<String>,
    ): GeoAskingMetricDto =
        GeoAskingMetricDto(
            id = filteredValues.toString(),
            idType = filteredColumn.toString(),
            totalRecords = result.getInt("total_records"),
            averageListingsDays = result.getBigDecimal("average_listing_days") ?: BigDecimal.ZERO,
            askingRent = buildGeoMetric(result, "rent")!!,
            squareFootage = buildGeoMetric(result, "unit_square_footage")!!,
            totalUnits = result.getInt("total_units"),
            bedrooms = result.getOptionalInt("bedrooms"),
            totalProperties = result.getInt("total_properties"),
        )

    private fun buildGeoMetric(
        result: Map<String, Any>,
        prefix: String,
    ): GeoMetric? {
        val min = result.getBigDecimal("${prefix}_min")
        val max = result.getBigDecimal("${prefix}_max")
        val average = result.getBigDecimal("$prefix")

        return takeIf { min != null && max != null && average != null }?.let {
            GeoMetric(min = min!!, max = max!!, average = average!!)
        }
    }
}
