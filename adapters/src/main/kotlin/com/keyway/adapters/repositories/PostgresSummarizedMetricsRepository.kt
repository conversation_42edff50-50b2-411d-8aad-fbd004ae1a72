package com.keyway.adapters.repositories

import com.keyway.adapters.repositories.utils.IdTypeUtils.getSqlColumn
import com.keyway.adapters.repositories.utils.PostgresQueryUtils.getBigDecimal
import com.keyway.adapters.repositories.utils.PostgresQueryUtils.getInt
import com.keyway.adapters.repositories.utils.PostgresQueryUtils.getOptionalInt
import com.keyway.adapters.repositories.utils.PostgresQueryUtils.getString
import com.keyway.adapters.repositories.utils.ViewsUtils.buildQueryCondition
import com.keyway.adapters.repositories.utils.ViewsUtils.generateViewsNames
import com.keyway.core.dto.GeoAskingMetricDto
import com.keyway.core.dto.GeoEffectiveMetricDto
import com.keyway.core.dto.GeoMetric
import com.keyway.core.dto.SummarizedMetricDto
import com.keyway.core.dto.query.metrics.IdType
import com.keyway.core.dto.query.metrics.MetricsFiltersQuery
import com.keyway.core.entities.metric.MetricType
import com.keyway.core.ports.repositories.SummarizedMetricsRepository
import com.keyway.kommons.db.SqlClient
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.async
import kotlinx.coroutines.withContext
import java.math.BigDecimal

class PostgresSummarizedMetricsRepository(
    private val sqlClient: SqlClient,
) : SummarizedMetricsRepository {
    companion object {
        const val RENT_LISTING_VIEW = "rent_listing_by"
        const val EFFECTIVE_RENT_VIEW = "effective_rent_by"
    }

    override suspend fun aggregateMetrics(metricsFiltersQuery: MetricsFiltersQuery): List<SummarizedMetricDto> =
        withContext(Dispatchers.IO) {
            val askingDeferred = async { getAskingMetrics(metricsFiltersQuery) }
            val effectiveDeferred = async { getEffectiveMetrics(metricsFiltersQuery) }

            val askingResult = askingDeferred.await()
            val effectiveResult = effectiveDeferred.await()

            val effectiveMap = effectiveResult.associateBy { it.code }
            askingResult.map { ask ->
                SummarizedMetricDto(ask, effectiveMap[ask.code])
            }
        }

    private fun buildUnitQuery(metricsFiltersQuery: MetricsFiltersQuery): String =
        buildQueryCondition(
            metricsFiltersQuery.idType,
            metricsFiltersQuery.ids.first(),
            metricsFiltersQuery.dateFrom,
            metricsFiltersQuery.dateTo,
        ).let { condition ->
            """
             WITH filtered_rent_listings as (
            ${generateViewsNames(RENT_LISTING_VIEW, metricsFiltersQuery.dateFrom, metricsFiltersQuery.dateTo)
                .joinToString(" UNION ALL ") {
                    " SELECT ${ "bedrooms,".takeIf { metricsFiltersQuery.type == MetricType.BEDROOMS } ?: "" }" +
                        "property_id ||'-'|| unnest(units) as unit from $it $condition "
                }}
                )
                SELECT  ${ "bedrooms,".takeIf { metricsFiltersQuery.type == MetricType.BEDROOMS } ?: "" }
                         COUNT(DISTINCT unit) AS total_units
                    FROM filtered_rent_listings
                    ${"GROUP BY bedrooms".takeIf { metricsFiltersQuery.type == MetricType.BEDROOMS } ?: ""}
            """.trimIndent()
        }

    private fun getAskingMetrics(metricsFiltersQuery: MetricsFiltersQuery): List<GeoAskingMetricDto> {
        val unitsResult =
            sqlClient.getAll(
                buildUnitQuery(metricsFiltersQuery),
                params = emptyList(),
            ) { result -> result }

        if (unitsResult.none { it.getInt("total_units") > 0 }) return emptyList()

        val mainResults =
            sqlClient.getAll(
                query = buildAskingMetricsQuery(metricsFiltersQuery),
                params = emptyList(),
            ) { result -> result }

        return mainResults.map { result ->
            buildGeoAskingDto(unitsResult, result, metricsFiltersQuery.idType)
        }
    }

    private fun buildAskingMetricsQuery(metricsFiltersQuery: MetricsFiltersQuery): String =
        buildQueryCondition(
            metricsFiltersQuery.idType,
            metricsFiltersQuery.ids.first(),
            metricsFiltersQuery.dateFrom,
            metricsFiltersQuery.dateTo,
        ).let { condition ->
            """
             WITH filtered_rent_listings as (
            ${generateViewsNames(RENT_LISTING_VIEW, metricsFiltersQuery.dateFrom, metricsFiltersQuery.dateTo)
                .joinToString(" UNION ALL ") { "SELECT * from $it $condition"}}
                )
                SELECT 
                        ${metricsFiltersQuery.idType.getSqlColumn()} as id,
                        ${ "bedrooms,".takeIf { metricsFiltersQuery.type == MetricType.BEDROOMS } ?: "" }
                        count (distinct property_id) as total_properties,
                        MIN(min_rent) AS rent_min,
                        MAX(max_rent) AS rent_max,
                        ROUND(SUM(rent_sum) / SUM(total_records), 2) AS rent,
                        MIN(min_sft) AS unit_square_footage_min,
                        MAX(max_sft) AS unit_square_footage_max,
                        ROUND(SUM(sft_sum) / SUM(total_records), 2) AS unit_square_footage,
                        count(*) AS total_records,
                        AVG(day_listing_since_publish) as average_listing_days
                    FROM filtered_rent_listings
                    GROUP BY ${metricsFiltersQuery.idType.getSqlColumn()}
                    ${" , bedrooms".takeIf { metricsFiltersQuery.type == MetricType.BEDROOMS } ?: ""}
            """.trimIndent()
        }

    private fun buildEffectiveMetricsQuery(metricsFiltersQuery: MetricsFiltersQuery): String =
        buildQueryCondition(
            metricsFiltersQuery.idType,
            metricsFiltersQuery.ids.first(),
            metricsFiltersQuery.dateFrom,
            metricsFiltersQuery.dateTo,
        ).let { condition ->
            """
             WITH filtered_rent_listings as (
            ${
                generateViewsNames(EFFECTIVE_RENT_VIEW, metricsFiltersQuery.dateFrom, metricsFiltersQuery.dateTo)
                    .joinToString(" UNION ALL ") { "SELECT * from $it $condition" }
            }
            )
            SELECT   
                    ${metricsFiltersQuery.idType.getSqlColumn()} as id,
                    ${"bedrooms,".takeIf { metricsFiltersQuery.type == MetricType.BEDROOMS } ?: ""}
                    count (distinct property_id) as total_properties,
                    MIN(min_rent) AS rent_min,
                    MAX(max_rent) AS rent_max,
                    ROUND(SUM(rent_sum) / SUM(total_records), 2) AS rent,
                    count(*) AS total_records,
                    AVG(day_listing_since_publish) as average_listing_days
                    FROM filtered_rent_listings
                    GROUP BY ${metricsFiltersQuery.idType.getSqlColumn()}
                 ${", bedrooms".takeIf { metricsFiltersQuery.type == MetricType.BEDROOMS } ?: ""}
            """.trimIndent()
        }

    private fun getEffectiveMetrics(metricsFiltersQuery: MetricsFiltersQuery): List<GeoEffectiveMetricDto> =
        sqlClient.getAll(
            query = buildEffectiveMetricsQuery(metricsFiltersQuery),
            params = emptyList(),
        ) { result ->
            buildGeoEffectiveDto(result, metricsFiltersQuery.idType, metricsFiltersQuery.ids)
        }

    private fun buildGeoEffectiveDto(
        result: Map<String, Any>,
        idType: IdType,
        idColumn: Set<String>,
    ): GeoEffectiveMetricDto =
        GeoEffectiveMetricDto(
            id = idColumn.toString(),
            idType = idType.toString(),
            bedrooms = result.getOptionalInt("bedrooms"),
            effectiveRent = buildGeoMetric(result, "rent"),
        )

    private fun buildGeoAskingDto(
        unitsResult: List<Map<String, Any>>,
        result: Map<String, Any>,
        filteredColumn: IdType,
    ): GeoAskingMetricDto =
        GeoAskingMetricDto(
            id = result.getString("id"),
            idType = filteredColumn,
            totalRecords = result.getInt("total_records"),
            averageListingsDays = result.getBigDecimal("average_listing_days") ?: BigDecimal.ZERO,
            askingRent = buildGeoMetric(result, "rent")!!,
            squareFootage = buildGeoMetric(result, "unit_square_footage")!!,
            totalUnits =
                result.getOptionalInt("bedrooms")?.let { bedrooms ->
                    // Si hay bedrooms, buscar en la lista unitsResult el que coincida con bedrooms
                    unitsResult
                        .find { it.getOptionalInt("bedrooms") == bedrooms }
                        ?.getInt("total_units")
                } ?: unitsResult.first().getInt("total_units"),
            bedrooms = result.getOptionalInt("bedrooms"),
            totalProperties = result.getInt("total_properties"),
        )

    private fun buildGeoMetric(
        result: Map<String, Any>,
        prefix: String,
    ): GeoMetric? {
        val min = result.getBigDecimal("${prefix}_min")
        val max = result.getBigDecimal("${prefix}_max")
        val average = result.getBigDecimal(prefix)

        return takeIf { min != null && max != null && average != null }?.let {
            GeoMetric(min = min!!, max = max!!, average = average!!)
        }
    }
}
