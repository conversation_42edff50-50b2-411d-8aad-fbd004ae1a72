package com.keyway.adapters.services

import datadog.trace.api.CorrelationIdentifier
import io.opentracing.Scope
import io.opentracing.Span
import io.opentracing.SpanContext
import io.opentracing.Tracer
import io.opentracing.propagation.Format
import io.opentracing.propagation.TextMapAdapter
import io.opentracing.util.GlobalTracer
import org.slf4j.LoggerFactory
import org.slf4j.MDC
import software.amazon.awssdk.services.sqs.model.MessageAttributeValue
import java.util.UUID

object TracerService {
    private const val TRACE_ID_KEY = "trace_id"
    private const val SPAN_ID_KEY = "span_id"
    private const val LOG_SPAN_ID_KEY = "dd.span_id"
    private const val LOG_TRACE_ID_KEY = "dd.trace_id"
    private const val CORRELATION_ID_VALUE_NAME = "X-Correlation-Id"

    private val logger = LoggerFactory.getLogger(this::class.java)
    private val tracer: Tracer = GlobalTracer.get()

    private fun generateTraceId() = UUID.randomUUID().toString().replace("-", "")

    fun Map<String, MessageAttributeValue>.extractOrGenerateCorrelationId() =
        this[CORRELATION_ID_VALUE_NAME]?.stringValue().also { logger.info("Trace extracted from {} with value {}", CORRELATION_ID_VALUE_NAME, it) }
            ?: generateTraceId().also { logger.info("Trace generated with value {}", it) }

    fun Map<String, MessageAttributeValue>.withCorrelationId(): Map<String, MessageAttributeValue> = this + (CORRELATION_ID_VALUE_NAME to generateMessageAttribute(generateTraceId()))

    private fun extractTraceContext(correlationId: String): SpanContext? = tracer.extract(Format.Builtin.TEXT_MAP, TextMapAdapter(mapOf(LOG_TRACE_ID_KEY to correlationId)))

    private fun createSpan(spanName: String): Span {
        val spanBuilder = tracer.buildSpan(spanName)
        return spanBuilder.start()
    }

    fun <T> newSpan(
        spanName: String,
        additionalMdc: Map<String, String> = emptyMap(),
        tracedOperation: () -> T,
    ): T {
        val span = createSpan(spanName)
        return span
            .activateSpan()
            .use {
                runCatching {
                    MDC.setContextMap(createMDCTracingMap(additionalMdc))
                    logger.debug("Span activated")
                    tracedOperation()
                }.also {
                    MDC.clear()
                    span.finish()
                }
            }.getOrThrow()
    }

    private fun createMDCTracingMap(additionalMdc: Map<String, String>) =
        mapOf(
            TRACE_ID_KEY to CorrelationIdentifier.getTraceId(),
            SPAN_ID_KEY to CorrelationIdentifier.getSpanId(),
        ) + additionalMdc

    private fun Span.activateSpan(): Scope = GlobalTracer.get().activateSpan(this)

    private fun generateMessageAttribute(traceId: String): MessageAttributeValue = MessageAttributeValue.builder().stringValue(traceId).build()
}
