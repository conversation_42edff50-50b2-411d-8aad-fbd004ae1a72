package com.keyway.adapters.services

import com.keyway.core.service.MessageBatchPublisher
import org.slf4j.LoggerFactory
import software.amazon.awssdk.services.sqs.SqsAsyncClient
import software.amazon.awssdk.services.sqs.model.SendMessageBatchRequest
import software.amazon.awssdk.services.sqs.model.SendMessageBatchRequestEntry
import java.util.UUID

class SqsMessageBatchPublisher(
    private val sqsClient: SqsAsyncClient,
    private val queueUrl: String,
) : MessageBatchPublisher {
    private val logger = LoggerFactory.getLogger(this::class.java)

    override fun publish(messages: List<String>) {
        if (messages.isEmpty()) return

        messages
            .map { message ->
                SendMessageBatchRequestEntry
                    .builder()
                    .id(UUID.randomUUID().toString()) // Add unique ID
                    .messageBody(message)
                    .build()
            }.let { messagesEntries ->
                messagesEntries
                    .chunked(10) // AWS SQS batch limit is 10
                    .forEach { chunk ->
                        try {
                            SendMessageBatchRequest
                                .builder()
                                .queueUrl(queueUrl)
                                .entries(chunk)
                                .build()
                                .let { sqsClient.sendMessageBatch(it) }
                        } catch (e: Exception) {
                            logger.error("Failed to send message batch", e)
                        }
                    }
            }
    }
}
