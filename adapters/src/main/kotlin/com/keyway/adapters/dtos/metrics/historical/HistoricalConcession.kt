package com.keyway.adapters.dtos.metrics.historical

import java.math.BigDecimal
import java.time.LocalDate

data class HistoricalConcessionSummary(
    val propertyId: String,
    override val values: List<HistoricalConcessionValue>,
) : HistoricalConcession

data class MsaHistoricalConcessionSummary(
    val msaCode: String,
    override val values: List<HistoricalConcessionValue>,
) : HistoricalConcession

data class ZipHistoricalConcessionSummary(
    val zipCode: String,
    override val values: List<HistoricalConcessionValue>,
) : HistoricalConcession

data class HistoricalConcessionValue(
    val dateFrom: LocalDate,
    val dateTo: LocalDate,
    val totalProperties: Int,
    val totalPropertiesWithConcession: Int,
    val avgConcessionValue: BigDecimal,
)

sealed interface HistoricalConcession {
    val values: List<HistoricalConcessionValue>
}
