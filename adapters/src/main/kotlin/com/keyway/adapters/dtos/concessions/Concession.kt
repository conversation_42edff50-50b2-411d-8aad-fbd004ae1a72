package com.keyway.adapters.dtos.concessions

import java.math.BigDecimal
import java.time.LocalDate

data class Concession(
    val propertyId: String,
    val dateFrom: LocalDate,
    val dateTo: LocalDate,
    val description: String,
    val active: Boolean,
    val benefits: List<Benefit>,
)

data class Benefit(
    val freeMonthsAmount: BigDecimal?,
    val freeMonthsUntil: LocalDate?,
    val oneTimeDollarsOffAmount: BigDecimal?,
    val oneTimeDollarsOffPercentage: BigDecimal?,
    val recurringDollarsOffAmount: BigDecimal?,
    val recurringDollarsOffPercentage: BigDecimal?,
    val recurringMonthsTerm: Int?,
    val leaseTermMonths: List<Int>?,
    val conditionDeadline: LocalDate?,
    val conditionBedrooms: List<Int>?,
    val conditionUnitNames: List<String>?,
    val conditionFloorplans: List<String>?,
    val conditionSelectedUnits: Boolean?,
    val conditionSelectedFloorplans: Boolean?,
    val conditionSelectedEmployees: Boolean?,
    val waivedApplicationFee: Boolean?,
    val waivedSecurityDeposit: Boolean?,
    val waivedAdministrativeFee: Boolean?,
    val waivedMoveInFee: Boolean?,
    val cheaperSecurityDeposit: Boolean?,
    val cheaperAdministrativeFee: Boolean?,
    val cheaperApplicationFee: Boolean?,
    val cheaperMoveInFee: Boolean?,
    val cheaperRent: Boolean?,
    val benefitGroupId: String?,
)
