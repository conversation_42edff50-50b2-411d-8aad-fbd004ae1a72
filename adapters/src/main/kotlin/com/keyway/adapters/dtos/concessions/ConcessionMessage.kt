package com.keyway.adapters.dtos.concessions

import java.math.BigDecimal
import java.time.LocalDate

data class ConcessionsMessage(
    val propertyId: String,
    val records: List<ConcessionRecord>,
)

data class ConcessionRecord(
    val concessionText: String,
    val recordDate: LocalDate,
    val benefits: List<ConcessionsBenefitMessage>,
)

data class ConcessionsBenefitMessage(
    val type: String?,
    val benefit: String?,
    val deadline: LocalDate?,
    val amountType: String?,
    val amountValue: BigDecimal?,
    val periodicityDuration: String?,
    val periodicityAmount: String?,
    val periodicityRecurrent: Boolean?,
    val requirements: String?,
)
