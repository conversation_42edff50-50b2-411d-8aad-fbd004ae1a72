package com.keyway.adapters.dtos.metrics.property

import com.keyway.adapters.dtos.metrics.MetricDetail
import java.math.BigDecimal
import java.time.LocalDate

data class BedroomRentSummary(
    val propertyId: String,
    val dateFrom: LocalDate,
    val dateTo: LocalDate,
    val metrics: List<BedroomRentMetric>,
)

data class BedroomRentMetric(
    val bedrooms: Int,
    val squareFootage: MetricDetail?,
    val askingRent: MetricDetail,
    val askingRentPSF: MetricDetail?,
    val effectiveRent: MetricDetail,
    val effectiveRentPSF: MetricDetail?,
    val deposit: MetricDetail?,
    val recordsQuantity: Int,
    val averageListingDays: BigDecimal,
    val unitsAvailable: Int,
    val totalUnits: Int,
)
