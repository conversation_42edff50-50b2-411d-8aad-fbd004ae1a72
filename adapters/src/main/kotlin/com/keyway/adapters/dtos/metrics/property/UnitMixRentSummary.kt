package com.keyway.adapters.dtos.metrics.property

import com.keyway.adapters.dtos.metrics.MetricDetail
import java.math.BigDecimal
import java.time.LocalDate

data class UnitMixRentSummary(
    val propertyId: String,
    val dateFrom: LocalDate,
    val dateTo: LocalDate,
    val metrics: List<UnitMixRentMetric>,
)

data class UnitMixRentMetric(
    val bedrooms: Int,
    val bathrooms: BigDecimal,
    val squareFootage: MetricDetail?,
    val askingRent: MetricDetail,
    val askingRentPSF: MetricDetail?,
    val effectiveRent: MetricDetail,
    val effectiveRentPSF: MetricDetail?,
    val deposit: MetricDetail?,
    val recordsQuantity: Int,
    val averageListingDays: BigDecimal,
    val unitsAvailable: Int,
    val totalUnits: Int,
)
