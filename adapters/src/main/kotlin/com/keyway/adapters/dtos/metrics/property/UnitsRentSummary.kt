package com.keyway.adapters.dtos.metrics.property

import com.keyway.adapters.dtos.metrics.MetricDetail
import java.math.BigDecimal
import java.time.LocalDate

data class UnitsRentSummary(
    val propertyId: String,
    val dateFrom: LocalDate,
    val dateTo: LocalDate,
    val metrics: List<UnitRentMetric>,
)

data class UnitRentMetric(
    val unitId: String,
    val floorPlan: String,
    val bedrooms: Int,
    val bathrooms: BigDecimal,
    val squareFootage: BigDecimal?,
    val askingRent: MetricDetail,
    val askingRentPSF: MetricDetail?,
    val effectiveRent: MetricDetail,
    val effectiveRentPSF: MetricDetail?,
    val deposit: MetricDetail?,
    val recordsQuantity: Int,
    val averageListingDays: BigDecimal,
)
