package com.keyway.adapters.dtos.listings

import com.keyway.core.entities.RentListingType
import java.math.BigDecimal
import java.time.LocalDate

enum class RentListingType {
    UNIT,
    FLOOR_PLAN,
}

data class RentListingResponse(
    val rentListingType: RentListingType,
    val unitId: String?,
    val floorPlan: String?,
    val askingRent: BigDecimal,
    val deposit: BigDecimal?,
    val squareFootage: BigDecimal?,
    val listingFrom: LocalDate,
    val listingTo: LocalDate,
    val active: Boolean,
    val availableOn: LocalDate?,
    val bedrooms: Int,
    val bathrooms: BigDecimal?,
    val effectiveRents: List<EffectiveRentResponse>,
)

data class PropertiesListingsResponse(
    val propertyId: String,
    val listings: List<RentListingResponse>,
)

data class RentListingsResponse(
    val propertyId: String,
    val units: List<RentListingResponse>,
)
