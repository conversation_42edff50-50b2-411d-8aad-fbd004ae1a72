package com.keyway.adapters.dtos.concessions

import com.keyway.core.entities.concessions.BenefitType
import com.keyway.core.entities.concessions.ConcessionBenefitAmountType
import com.keyway.core.entities.concessions.ConcessionBenefitTargetType
import com.keyway.core.entities.concessions.ConcessionBenefitType
import com.keyway.core.entities.concessions.ConcessionPeriodicityDuration
import java.math.BigDecimal
import java.time.LocalDate

data class ConcessionResponse(
    val propertyId: String,
    val dateFrom: LocalDate,
    val dateTo: LocalDate,
    val description: String,
    val active: Boolean,
    val benefits: List<ConcessionBenefitResponse>,
)

data class ConcessionBenefitResponse(
    val type: ConcessionBenefitType?,
    val benefitType: BenefitType?,
    val deadline: LocalDate?,
    val amountType: ConcessionBenefitAmountType?,
    val amountValue: BigDecimal?,
    val periodicityDuration: ConcessionPeriodicityDuration?,
    val periodicityAmount: BigDecimal?,
    val periodicityRecurrent: Boolean?,
    val targetType: ConcessionBenefitTargetType = ConcessionBenefitTargetType.PROPERTY,
    val applicableUnitType: List<String>?,
)
