package com.keyway.adapters.dtos.units

import java.math.BigDecimal

data class PropertyUnitMessage(
    val propertyId: String,
    val units: List<UnitMessage>,
)

data class UnitMessage(
    val id: String,
    val squareFootage: BigDecimal?,
    val bedrooms: Int,
    val bathrooms: BigDecimal?,
    val floorPlan: String?,
    val amenities: Set<String>,
    val renovationProbability: BigDecimal?,
    val renovated: Boolean?,
)
