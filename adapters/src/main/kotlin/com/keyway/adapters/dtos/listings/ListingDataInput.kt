package com.keyway.adapters.dtos.listings

import com.keyway.core.entities.RentListingType
import java.math.BigDecimal
import java.time.LocalDate

data class ListingDataInput(
    val propertyId: String,
    val unitId: String?,
    val type: RentListingType?,
    val floorPlan: String?,
    val dateFrom: LocalDate?,
    val dateTo: LocalDate?,
    val bedrooms: Int?,
    val bathrooms: BigDecimal?,
)

data class PropertiesListingDataInput(
    val propertiesIds: Set<String>,
    val dateFrom: LocalDate?,
    val dateTo: LocalDate?,
    val bedrooms: Int?,
    val bathrooms: BigDecimal?,
)
