package com.keyway.adapters.dtos.multifamily

import java.math.BigDecimal

data class MultifamilyRealEstateData(
    val source: String,
    val name: String? = null,
    val propertyStyle: String? = null,
    val propertyStatus: String? = null,
    val owner: String? = null,
    val managerCompany: String? = null,
    val phone: String? = null,
    val website: String? = null,
    val sourceLink: String? = null,
    val occupancyPercentage: BigDecimal? = null,
    val constructionYear: Int? = null,
    val renovationYear: Int? = null,
    val leaseRatePercentage: BigDecimal? = null,
    val askingPrice: BigDecimal? = null,
    val estimatedPrice: BigDecimal? = null,
    val estimatedPricePerUnit: BigDecimal? = null,
    val productType: String? = null,
    val propertyClass: String? = null,
    val parkingRatio: BigDecimal? = null,
    val units: MultifamilyUnits? = null,
    val unitMix: MultifamilyUnits? = null,
    val communityAmenities: List<String> = listOf(),
    val apartmentsAmenities: List<String> = listOf(),
    val propertyAmenities: Set<String> = setOf(),
    val unitsAmenities: Set<String> = setOf(),
    val amenities: List<String> = listOf(),
    val images: List<String> = listOf(),
    val stories: Int? = null,
    val landSizeSqft: BigDecimal? = null,
    val housingSegment: List<String>? = emptyList(),
    val hasAffordableUnits: Boolean? = null,
)

data class MultifamilyUnits(
    val quantity: Int? = null,
    val records: List<MultifamilyUnitsRecord> = listOf(),
)

data class MultifamilyUnitsRecord(
    val bathrooms: BigDecimal? = null,
    val bedrooms: BigDecimal? = null,
    val quantity: Int? = null,
    val squareFootage: BigDecimal? = null,
    val rent: BigDecimal? = null,
    val marketRent: BigDecimal? = null,
    val renoRent: BigDecimal? = null,
    val askingRent: BigDecimal? = null,
    val estimatedRent: BigDecimal? = null,
    val marketRenoRent: BigDecimal? = null,
    val studio: Boolean? = null,
    val penthouse: Boolean? = null,
    val loft: Boolean? = null,
    val den: Boolean? = null,
    val townhouse: Boolean? = null,
)
