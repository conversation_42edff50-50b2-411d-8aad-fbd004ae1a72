package com.keyway.adapters.dtos.listings

import java.math.BigDecimal

data class UnitRentSuggestions(
    val propertyId: String,
    val unitRentSuggestions: List<UnitRentSuggestion>,
)

data class UnitRentSuggestion(
    val unitId: String,
    val askingRent: BigDecimal,
    val effectiveRent: BigDecimal,
    val similarUnitComparisons: List<UnitComparison>,
)

data class UnitComparison(
    val propertyId: String,
    val unitId: String,
    val similarityScore: Double,
)
