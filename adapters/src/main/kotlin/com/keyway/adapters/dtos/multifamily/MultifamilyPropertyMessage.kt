package com.keyway.adapters.dtos.multifamily

import com.keyway.core.entities.GeoPoint
import java.math.BigDecimal

data class MultifamilyPropertyMessage(
    val id: String,
    val address: String,
    val fullAddress: String,
    val city: String,
    val county: String?,
    val zipCode: Long,
    val state: String,
    val propertyType: String,
    val location: GeoPoint,
    val squareFootage: BigDecimal?,
    val sourceType: String?,
    val tractCode: Long?,
    val constructionYear: Int?,
    val renovationYear: Int?,
    val active: Boolean,
    val additionalData: MultifamilyPropertyAdditionalData?,
)
