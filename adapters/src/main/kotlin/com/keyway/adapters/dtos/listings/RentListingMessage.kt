package com.keyway.adapters.dtos.listings

import com.fasterxml.jackson.annotation.JsonProperty
import com.keyway.core.entities.RentListingType
import java.math.BigDecimal
import java.time.LocalDate

data class RentListingMessage(
    val recordId: String?,
    val recordSource: String,
    val propertyId: String,
    val type: RentListingType = RentListingType.UNIT,
    val unitId: String,
    val zipCode: String,
    val msaCode: String,
    val unitSquareFootage: BigDecimal?,
    val bedroomsQuantity: Int,
    val bathroomsQuantity: BigDecimal?,
    val floorPlan: String?,
    val availableIn: LocalDate?,
    val rent: BigDecimal,
    val rentDeposit: BigDecimal?,
    val recordDate: LocalDate,
    val effectiveRent: BigDecimal,
    val effectiveRentDeposit: BigDecimal?,
    val concessionText: String?,
)

data class GroupedRentListingMessage(
    val propertyId: String,
    val type: RentListingType = RentListingType.UNIT,
    val unitId: String,
    val zipCode: String,
    val msaCode: String,
    val bedroomsQuantity: Int,
    val bathroomsQuantity: BigDecimal?,
    val floorPlan: String?,
    val unitSquareFootage: BigDecimal?,
    val records: List<RentUnitListingMessage>,
)

data class RentUnitListingMessage(
    @JsonProperty("rs")
    val recordSource: String,
    @JsonProperty("r")
    val rent: BigDecimal,
    @JsonProperty("rd")
    val rentDeposit: BigDecimal?,
    @JsonProperty("er")
    val effectiveRent: BigDecimal,
    @JsonProperty("erd")
    val effectiveRentDeposit: BigDecimal?,
    @JsonProperty("ai")
    val availableIn: LocalDate?,
    @JsonProperty("dor")
    val recordDate: LocalDate,
    @JsonProperty("ct")
    val concessionText: String? = "",
)
