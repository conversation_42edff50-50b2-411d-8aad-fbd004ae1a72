import org.jetbrains.kotlin.gradle.tasks.KotlinCompile

val kommonsMapperVersion: String by rootProject
val httpclient5Version: String by rootProject
val jacksonVersion: String by rootProject
val kommonsSqsVersion: String by rootProject
val awsSdkVersion: String by rootProject
val kommonsDbVersion: String by rootProject
val postgresVersion: String by rootProject
val awsKotlinSdkVersion: String by rootProject
val datadogVersion: String by rootProject

plugins {
    kotlin("jvm")
    kotlin("kapt")
}

dependencies {
    implementation(project(":core"))

    // Rest
    implementation("org.apache.httpcomponents.client5:httpclient5:$httpclient5Version")

    // Mapper
    implementation("com.fasterxml.jackson.core:jackson-annotations:$jacksonVersion")
    implementation("com.keyway:kommons-mapper:$kommonsMapperVersion") {
        exclude(group = "com.google.code.gson")
    }

    // AWS
    implementation(platform("software.amazon.awssdk:bom:$awsSdkVersion"))
    implementation("software.amazon.awssdk:sqs")

    // SQS
    implementation("com.keyway:kommons-sqs:$kommonsSqsVersion")

    // DB
    implementation("com.keyway:kommons-db:$kommonsDbVersion")
    implementation("org.postgresql:postgresql:$postgresVersion")

    implementation("org.jetbrains.kotlinx:kotlinx-coroutines-core:1.10.2")

    implementation("aws.sdk.kotlin:cloudwatch:$awsKotlinSdkVersion")

    // Datadog
    implementation("com.datadoghq:dd-trace-api:$datadogVersion")
    implementation("com.datadoghq:dd-trace-ot:$datadogVersion")
}

tasks.withType<KotlinCompile> {
    kotlinOptions.jvmTarget = JavaVersion.VERSION_17.toString()
}
