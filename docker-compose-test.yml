version: '3.9'

services:
  test:
    image: amazoncorretto:17-alpine
    depends_on:
      - postgres
      - localstack
    platform: linux/amd64
    volumes:
      - .:/ws
    working_dir: /ws
    environment:
      GH_USERNAME: ${GH_USERNAME}
      GH_TOKEN: ${GH_TOKEN}
    command: >
      ./gradlew --no-daemon -g ./.gradle-cache ktlintCheck test --info

  postgres:
    image: postgis/postgis:latest
    container_name: "rent-api_postgres"
    restart: always
    environment:
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: postgres
      POSTGRES_DB: rent
      POSTGRES_HOST_AUTH_METHOD: md5
    volumes:
      - 'postgresdb-volume:/var/lib/postgresql/data'
    ports:
      - "5432:5432"


  localstack:
    container_name: "rent-api_localstack"
    image: localstack/localstack:latest
    ports:
      - "4566:4566"
      - "4571:4571"
      - "4510-4559:4510-4559"
    environment:
      - AWS_DEFAULT_REGION=us-east-1
      - SERVICES=dynamodb,sqs,sns,s3
      - DOCKER_HOST=unix:///var/run/docker.sock
    volumes:
      - "${TMPDIR:-/tmp}/localstack:/var/lib/localstack"
      - "/var/run/docker.sock:/var/run/docker.sock"
      - "./aws_local_resources.sh:/etc/localstack/init/ready.d/aws_local_resources.sh"

volumes:
  postgresdb-volume: